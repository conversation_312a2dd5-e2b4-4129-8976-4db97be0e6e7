package com.mlc.ai.service.workflow.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.ai.prompt.store.dag.node.OutsideBorderNode;
import com.mlc.ai.service.MlcAiServiceConstants;
import com.mlc.ai.task.MlcAiTaskConstants;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.context.AIContextExecutor;
import com.mlc.ai.task.executor.AITaskExecutor;
import com.mlc.ai.task.model.AITaskModel;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Strings;


/**
 * 边界分析任务执行器
 * 负责处理用户原始需求的边界分析，识别哪些部分属于领域坐标系内，哪些属于边界外
 */
@Slf4j
public class BoundaryAnalysisTaskExecutor extends AITaskExecutor {

    @Override
    protected void justInLLMExecutor(AIContextExecutor llmExecutor, AITaskModel aiTask, ExecutionContext context) {
        // 添加边界分析特定的处理器
        llmExecutor.withStreamProcessor(AIContextExecutor::codeBlockExtractorProcessor)
        .withResultProcessor((response, task, ctx) -> {
            // 解析LLM返回的JSON结果
            try {
                OutsideBorderNode outsideBorderNode = new ObjectMapper().readValue(response, OutsideBorderNode.class);
                String remainingStatement = outsideBorderNode.getRemainingStatement();

                // 存储结果到ExecutionContext，供后续任务使用
                context.setAttribute(MlcAiServiceConstants.BOUNDARY_ANALYSIS_RESULT, outsideBorderNode);
                context.setAttribute(MlcAiTaskConstants.PREVIOUS_TASK_REQUEST, remainingStatement);

                // 判断是否需要继续澄清问题流程
                boolean needsClarification = !Strings.isNullOrEmpty(remainingStatement);
                task.getTaskContext().getOutput().put("needsClarification", needsClarification);

                log.info("[{}] 边界分析完成，剩余语句: {}", context.getExecutionId(), remainingStatement);

                return needsClarification ? "请继续澄清问题。" : "边界分析已完成，无需进一步澄清。";
            } catch (Exception e) {
                log.error("[{}] 解析边界分析结果失败: {}", context.getExecutionId(), e.getMessage());
                throw new RuntimeException("解析边界分析结果失败: " + e.getMessage());
            }
        });
    }
}