<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>mlc-ai</artifactId>
    <groupId>flowweb-zhongzhi</groupId>
    <version>${revision}</version>
  </parent>

  <artifactId>mlc-ai-service</artifactId>

  <modelVersion>4.0.0</modelVersion>

  <dependencies>
    <dependency>
      <groupId>flowweb-zhongzhi</groupId>
      <artifactId>mlc-ai-dsl</artifactId>
    </dependency>

    <dependency>
      <groupId>flowweb-zhongzhi</groupId>
      <artifactId>mlc-ai-prompt</artifactId>
    </dependency>

    <dependency>
      <groupId>flowweb-zhongzhi</groupId>
      <artifactId>mlc-ai-task</artifactId>
    </dependency>

    <dependency>
      <groupId>io.github.entropy-cloud</groupId>
      <artifactId>nop-ioc</artifactId>
    </dependency>

    <dependency>
      <groupId>io.github.entropy-cloud</groupId>
      <artifactId>nop-ai-core</artifactId>
    </dependency>
  </dependencies>
</project>