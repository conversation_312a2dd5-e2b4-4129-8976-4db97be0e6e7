package com.mlc.ai.prompt.store.dag.propagation.part;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.event.NodeStatusChangeEvent;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import com.mlc.ai.prompt.store.dag.node.base.NodeType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 批量状态传播器
 * 负责高效的批量状态传播，避免递归调用和重复计算
 */
@Slf4j
public class BatchStatusPropagator {
    
    private final DagGraph dagGraph;
    private final DependencyQueryService dependencyService;
    
    // 批量处理队列，避免递归传播
    private final Queue<NodeStatusChangeEvent> pendingEvents = new ConcurrentLinkedQueue<>();
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);
    
    // 性能优化：缓存最近计算的结果
    private final Map<String, Boolean> readinessCache = new HashMap<>();
    private volatile long cacheVersion = 0;
    
    public BatchStatusPropagator(DagGraph dagGraph, DependencyQueryService dependencyService) {
        this.dagGraph = dagGraph;
        this.dependencyService = dependencyService;
    }
    
    /**
     * 处理节点状态变化事件
     */
    public void processNodeStatusChange(NodeStatusChangeEvent event) {
        pendingEvents.offer(event);
        
        // 使用 CAS 确保只有一个线程在处理
        if (isProcessing.compareAndSet(false, true)) {
            try {
                processPendingEventsInBatch();
            } finally {
                isProcessing.set(false);
            }
        }
    }
    
    /**
     * 批量处理所有待处理的事件
     */
    private void processPendingEventsInBatch() {
        Set<String> affectedNodes = new HashSet<>();
        
        // 批量收集所有受影响的节点
        NodeStatusChangeEvent event;
        while ((event = pendingEvents.poll()) != null) {
            collectAffectedNodes(event, affectedNodes);
        }
        
        if (!affectedNodes.isEmpty()) {
            log.debug("批量处理 {} 个受影响的节点", affectedNodes.size());
            // 批量更新状态，减少递归调用
            updateAffectedNodesInBatch(affectedNodes);
            // 清除缓存，因为状态已发生变化
            invalidateCache();
        }
    }
    
    /**
     * 收集受影响的节点
     */
    private void collectAffectedNodes(NodeStatusChangeEvent event, Set<String> affectedNodes) {
        IDagNode changedNode = event.getNode();
        NodeStatus newStatus = event.getNewStatus();
        
        log.debug("收集受影响节点 - 节点: {}, 新状态: {}", changedNode.getId(), newStatus);
        
        // 如果节点变为就绪状态，收集所有消费者节点
        if (NodeStatus.isNodeReady(changedNode)) {
            List<IDagNode> consumers = dependencyService.findConsumersOfProvider(changedNode);
            for (IDagNode consumer : consumers) {
                affectedNodes.add(consumer.getId());
            }
            log.debug("节点 {} 变为就绪状态，影响 {} 个消费者", changedNode.getId(), consumers.size());
        }
        
        // 处理特殊状态的传播
        handleSpecialStatusPropagation(changedNode, newStatus, affectedNodes);
    }
    
    /**
     * 处理特殊状态的传播
     */
    private void handleSpecialStatusPropagation(IDagNode changedNode, NodeStatus newStatus, Set<String> affectedNodes) {
        switch (newStatus) {
            case ASSUMPTION_MADE -> handleAssumptionMade(changedNode, affectedNodes);
            case CONFLICTED -> handleConflicted(changedNode, affectedNodes);
            case FAILED -> handleFailed(changedNode, affectedNodes);
            case ASSUMPTION_INVALIDATED -> handleAssumptionInvalidated(changedNode, affectedNodes);
            case REQUIRES_CLARIFY -> handleRequiresClarify(changedNode, affectedNodes);
        }
    }
    
    /**
     * 批量更新受影响的节点
     */
    private void updateAffectedNodesInBatch(Set<String> affectedNodeIds) {
        // 按优先级排序：先处理参数节点，再处理任务节点
        List<IDagNode> sortedNodes = affectedNodeIds.stream()
                .map(dagGraph::getNode)
                .filter(Objects::nonNull)
                .sorted(this::compareNodePriority)
                .toList();
        
        for (IDagNode node : sortedNodes) {
            if (shouldUpdateNode(node)) {
                updateNodeIfReady(node);
            }
        }
    }
    
    /**
     * 节点优先级比较器：参数节点 > 其他节点 > 任务节点
     */
    private int compareNodePriority(IDagNode a, IDagNode b) {
        return Integer.compare(getNodePriority(a), getNodePriority(b));
    }
    
    private int getNodePriority(IDagNode node) {
        return switch (node.getType()) {
            case PARAMETER -> 1;
            case STRUCTURED_TASK -> 3;
            default -> 2;
        };
    }
    
    /**
     * 判断节点是否需要更新
     */
    private boolean shouldUpdateNode(IDagNode node) {
        return node.getStatus() == NodeStatus.AWAITING_DEPENDENCIES || 
               node.getStatus() == NodeStatus.PENDING_ANALYSIS ||
               node.getStatus() == NodeStatus.AWAITING_VALIDATION;
    }
    
    /**
     * 更新单个节点状态（如果就绪）
     */
    private void updateNodeIfReady(IDagNode node) {
        String cacheKey = node.getId() + "_" + cacheVersion;
        Boolean cachedResult = readinessCache.get(cacheKey);
        
        List<IDagNode> providers = dependencyService.findProvidersForConsumer(node);
        boolean allProvidersReady;
        
        if (cachedResult != null) {
            allProvidersReady = cachedResult;
            log.debug("使用缓存结果：节点 {} 的依赖就绪状态: {}", node.getId(), allProvidersReady);
        } else {
            allProvidersReady = dependencyService.areAllProvidersReady(providers);
            readinessCache.put(cacheKey, allProvidersReady);
        }
        
        log.debug("检查节点 {} 是否就绪，当前状态: {}, 依赖提供者数量: {}, 全部就绪: {}", 
                 node.getId(), node.getStatus(), providers.size(), allProvidersReady);

        if (allProvidersReady) {
            NodeStatus nextStatus = NodeStatusStateMachine.determineNextStatus(node, providers);
            log.debug("所有提供者就绪，节点 {} 下一状态应为: {}", node.getId(), nextStatus);
            
            if (nextStatus != node.getStatus()) {
                NodeStatus oldStatus = node.getStatus();
                dagGraph.updateNodeStatus(node.getId(), nextStatus);
                log.info("批量更新节点 {} 状态从 {} 到 {}", node.getId(), oldStatus, nextStatus);
                
                // 如果节点变为就绪状态，立即传播到其消费者
                if (NodeStatus.isNodeReady(node)) {
                    propagateToConsumersImmediate(node);
                }
            } else {
                log.debug("节点 {} 状态无需更新，保持: {}", node.getId(), node.getStatus());
            }
        } else {
            log.debug("节点 {} 的依赖尚未全部就绪，保持 {} 状态", node.getId(), node.getStatus());
        }
    }
    
    /**
     * 立即传播状态到消费者节点（用于关键路径优化）
     */
    private void propagateToConsumersImmediate(IDagNode readyNode) {
        List<IDagNode> consumers = dependencyService.findConsumersOfProvider(readyNode);
        log.debug("立即传播：节点 {} 变为就绪，检查 {} 个消费者", readyNode.getId(), consumers.size());
        
        for (IDagNode consumer : consumers) {
            if (shouldUpdateNode(consumer)) {
                updateNodeIfReady(consumer);
            }
        }
    }
    
    /**
     * 强制检查所有节点的状态
     */
    public void forceCheckAllNodes() {
        log.info("开始强制检查所有节点状态...");
        
        List<IDagNode> allNodes = new ArrayList<>(dagGraph.getAllNodes());
        
        // 按优先级分组处理
        Map<Integer, List<IDagNode>> nodesByPriority = new HashMap<>();
        for (IDagNode node : allNodes) {
            int priority = getNodePriority(node);
            nodesByPriority.computeIfAbsent(priority, k -> new ArrayList<>()).add(node);
        }
        
        // 按优先级顺序处理
        for (int priority = 1; priority <= 3; priority++) {
            List<IDagNode> nodes = nodesByPriority.get(priority);
            if (nodes != null) {
                for (IDagNode node : nodes) {
                    if (shouldUpdateNode(node)) {
                        log.debug("强制检查节点: {} (当前状态: {})", node.getId(), node.getStatus());
                        updateNodeIfReady(node);
                    }
                }
            }
        }
        
        log.info("强制状态检查完成");
    }
    
    /**
     * 处理假设状态传播
     */
    private void handleAssumptionMade(IDagNode assumptionNode, Set<String> affectedNodes) {
        List<IDagNode> consumers = dependencyService.findConsumersOfProvider(assumptionNode);
        for (IDagNode consumer : consumers) {
            if (consumer.getStatus() == NodeStatus.AWAITING_DEPENDENCIES) {
                if (consumer.getType() == NodeType.PARAMETER) {
                    // 参数节点直接设置为假设状态
                    dagGraph.updateNodeStatus(consumer.getId(), NodeStatus.ASSUMPTION_MADE);
                } else {
                    // 其他节点检查是否可以进入等待验证状态
                    affectedNodes.add(consumer.getId());
                }
            }
        }
    }
    
    /**
     * 处理冲突状态传播
     */
    private void handleConflicted(IDagNode conflictedNode, Set<String> affectedNodes) {
        List<IDagNode> consumers = dependencyService.findConsumersOfProvider(conflictedNode);
        for (IDagNode consumer : consumers) {
            if (dependencyService.isDependencyCritical(conflictedNode, consumer)) {
                dagGraph.updateNodeStatus(consumer.getId(), NodeStatus.CONFLICTED);
            }
        }
    }
    
    /**
     * 处理失败状态传播
     */
    private void handleFailed(IDagNode failedNode, Set<String> affectedNodes) {
        List<IDagNode> consumers = dependencyService.findConsumersOfProvider(failedNode);
        for (IDagNode consumer : consumers) {
            // 检查是否可以基于其他依赖继续
            if (consumer.getStatus() != NodeStatus.FAILED) {
                boolean canContinueWithOtherProviders = dependencyService.areOtherProvidersReady(consumer, failedNode);
                
                if (!canContinueWithOtherProviders && dependencyService.isStrongDependency(failedNode, consumer)) {
                    dagGraph.updateNodeStatus(consumer.getId(), NodeStatus.FAILED);
                    
                    // 获取所有传递性消费者，进行级联失败处理
                    Set<IDagNode> transitiveConsumers = dependencyService.getTransitiveConsumers(consumer);
                    for (IDagNode transitiveConsumer : transitiveConsumers) {
                        if (dependencyService.isDependencyCritical(consumer, transitiveConsumer)) {
                            affectedNodes.add(transitiveConsumer.getId());
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 处理假设无效状态传播
     */
    private void handleAssumptionInvalidated(IDagNode invalidAssumptionNode, Set<String> affectedNodes) {
        List<IDagNode> consumers = dependencyService.findConsumersOfProvider(invalidAssumptionNode);
        for (IDagNode consumer : consumers) {
            if (consumer.getStatus() == NodeStatus.RESOLVED || 
                consumer.getStatus() == NodeStatus.READY_FOR_EXECUTION) {
                dagGraph.updateNodeStatus(consumer.getId(), NodeStatus.AWAITING_DEPENDENCIES);
            }
        }
    }
    
    /**
     * 处理需要澄清状态传播
     */
    private void handleRequiresClarify(IDagNode clarifyNode, Set<String> affectedNodes) {
        List<IDagNode> consumers = dependencyService.findConsumersOfProvider(clarifyNode);
        for (IDagNode consumer : consumers) {
            if (consumer.getType() == NodeType.STRUCTURED_TASK) {
                dagGraph.updateNodeStatus(consumer.getId(), NodeStatus.AWAITING_DEPENDENCIES);
            }
        }
    }
    
    /**
     * 清除缓存
     */
    private void invalidateCache() {
        cacheVersion++;
        if (readinessCache.size() > 1000) { // 防止缓存过大
            readinessCache.clear();
        }
    }
}