package com.mlc.ai.prompt.store.dag.handler.constraint;

import com.mlc.ai.prompt.store.dag.DagGraph;
import com.mlc.ai.prompt.store.dag.node.base.IDagNode;
import com.mlc.ai.prompt.store.dag.node.base.NodeStatus;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 冲突约束管理器
 * <p>
 * 负责管理DAG中节点间的冲突约束，独立于边类型系统。
 * 冲突是一种元数据约束，不影响图的拓扑结构，但会影响Orchestrator的调度决策。
 * <p>
 * 设计理念：
 * 1. 冲突不通过边类型表示，避免与执行流程的语义混淆
 * 2. 冲突作为独立约束存储在图的元数据中
 * 3. 任务调度器在调度时先检查边类型条件，再检查冲突约束
 * 4. 支持冲突解决机制和澄清问题生成
 */
@Slf4j
public class ConflictConstraintManager {
    
    private final DagGraph dagGraph;
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    
    // 冲突约束存储：冲突对 -> 冲突描述
    private final Map<ConflictPair, ConflictConstraint> conflictConstraints = new ConcurrentHashMap<>();
    
    // 节点到冲突的索引，用于快速查找
    private final Map<String, Set<ConflictPair>> nodeToConflicts = new ConcurrentHashMap<>();
    
    public ConflictConstraintManager(DagGraph dagGraph) {
        this.dagGraph = dagGraph;
    }
    
    /**
     * 添加冲突约束
     * @param node1 冲突节点1
     * @param node2 冲突节点2
     * @param description 冲突描述
     * @param conflictType 冲突类型
     * @return 如果成功添加则返回true
     */
    public boolean addConflictConstraint(IDagNode node1, IDagNode node2, String description, ConflictType conflictType) {
        lock.writeLock().lock();
        try {
            ConflictPair pair = new ConflictPair(node1.getId(), node2.getId());
            
            // 检查是否已存在此冲突
            if (conflictConstraints.containsKey(pair)) {
                log.info("冲突约束已存在: {} <-> {}", node1.getId(), node2.getId());
                return false;
            }
            
            ConflictConstraint constraint = new ConflictConstraint(
                node1.getId(), 
                node2.getId(), 
                description, 
                conflictType,
                System.currentTimeMillis()
            );
            
            // 添加冲突约束
            conflictConstraints.put(pair, constraint);
            
            // 更新索引
            nodeToConflicts.computeIfAbsent(node1.getId(), k -> ConcurrentHashMap.newKeySet()).add(pair);
            nodeToConflicts.computeIfAbsent(node2.getId(), k -> ConcurrentHashMap.newKeySet()).add(pair);
            
            // 标记节点状态为冲突
            markNodesAsConflicted(node1, node2);
            
            log.info("添加冲突约束: {} <-> {}, 类型: {}, 描述: {}", 
                    node1.getId(), node2.getId(), conflictType, description);
            
            return true;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 检查两个节点是否存在冲突约束
     */
    public boolean hasConflict(String nodeId1, String nodeId2) {
        lock.readLock().lock();
        try {
            ConflictPair pair = new ConflictPair(nodeId1, nodeId2);
            return conflictConstraints.containsKey(pair);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 获取节点的所有冲突约束
     */
    public Set<ConflictConstraint> getConflictsForNode(String nodeId) {
        lock.readLock().lock();
        try {
            Set<ConflictPair> pairs = nodeToConflicts.get(nodeId);
            if (pairs == null) {
                return Collections.emptySet();
            }
            
            Set<ConflictConstraint> conflicts = new HashSet<>();
            for (ConflictPair pair : pairs) {
                ConflictConstraint constraint = conflictConstraints.get(pair);
                if (constraint != null) {
                    conflicts.add(constraint);
                }
            }
            return conflicts;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 获取所有未解决的冲突约束
     */
    public List<ConflictConstraint> getUnresolvedConflicts() {
        lock.readLock().lock();
        try {
            return conflictConstraints.values().stream()
                    .filter(constraint -> !constraint.isResolved())
                    .toList();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 解决冲突约束
     * @param nodeId1 节点1 ID
     * @param nodeId2 节点2 ID
     * @param resolution 解决方案描述
     */
    public boolean resolveConflict(String nodeId1, String nodeId2, String resolution) {
        lock.writeLock().lock();
        try {
            ConflictPair pair = new ConflictPair(nodeId1, nodeId2);
            ConflictConstraint constraint = conflictConstraints.get(pair);
            
            if (constraint == null) {
                log.warn("尝试解决不存在的冲突: {} <-> {}", nodeId1, nodeId2);
                return false;
            }
            
            constraint.resolve(resolution);
            
            // 更新节点状态
            IDagNode node1 = dagGraph.getNode(nodeId1);
            IDagNode node2 = dagGraph.getNode(nodeId2);
            
            if (node1 != null && node1.getStatus() == NodeStatus.CONFLICTED) {
                // 检查节点是否还有其他未解决的冲突
                boolean hasOtherConflicts = getConflictsForNode(nodeId1).stream()
                        .anyMatch(c -> !c.isResolved() && !c.equals(constraint));
                
                if (!hasOtherConflicts) {
                    dagGraph.updateNodeStatus(nodeId1, NodeStatus.RESOLVED);
                }
            }
            
            if (node2 != null && node2.getStatus() == NodeStatus.CONFLICTED) {
                boolean hasOtherConflicts = getConflictsForNode(nodeId2).stream()
                        .anyMatch(c -> !c.isResolved() && !c.equals(constraint));
                
                if (!hasOtherConflicts) {
                    dagGraph.updateNodeStatus(nodeId2, NodeStatus.RESOLVED);
                }
            }
            
            log.info("解决冲突约束: {} <-> {}, 解决方案: {}", nodeId1, nodeId2, resolution);
            return true;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 移除冲突约束
     */
    public boolean removeConflictConstraint(String nodeId1, String nodeId2) {
        lock.writeLock().lock();
        try {
            ConflictPair pair = new ConflictPair(nodeId1, nodeId2);
            ConflictConstraint removed = conflictConstraints.remove(pair);
            
            if (removed != null) {
                // 更新索引
                Set<ConflictPair> node1Conflicts = nodeToConflicts.get(nodeId1);
                if (node1Conflicts != null) {
                    node1Conflicts.remove(pair);
                    if (node1Conflicts.isEmpty()) {
                        nodeToConflicts.remove(nodeId1);
                    }
                }
                
                Set<ConflictPair> node2Conflicts = nodeToConflicts.get(nodeId2);
                if (node2Conflicts != null) {
                    node2Conflicts.remove(pair);
                    if (node2Conflicts.isEmpty()) {
                        nodeToConflicts.remove(nodeId2);
                    }
                }
                
                log.info("移除冲突约束: {} <-> {}", nodeId1, nodeId2);
                return true;
            }
            
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 检查节点是否可以被调度执行
     * <p>
     * 此方法供Orchestrator调用，在检查边类型条件满足后，
     * 进一步检查是否有冲突约束阻止执行
     * 
     * @param nodeId 要检查的节点ID
     * @return 如果没有冲突阻止执行则返回true
     */
    public boolean canNodeBeScheduled(String nodeId) {
        lock.readLock().lock();
        try {
            Set<ConflictConstraint> conflicts = getConflictsForNode(nodeId);
            
            // 检查是否有未解决的冲突
            for (ConflictConstraint conflict : conflicts) {
                if (!conflict.isResolved()) {
                    // 检查冲突的另一个节点状态
                    String otherNodeId = conflict.getNode1Id().equals(nodeId) ? 
                            conflict.getNode2Id() : conflict.getNode1Id();
                    
                    IDagNode otherNode = dagGraph.getNode(otherNodeId);
                    if (otherNode != null) {
                        // 如果冲突节点处于活跃状态，则当前节点不能被调度
                        if (isNodeActive(otherNode)) {
                            log.debug("节点 {} 不能被调度，因为与活跃节点 {} 存在未解决的冲突", 
                                    nodeId, otherNodeId);
                            return false;
                        }
                    }
                }
            }
            
            return true;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 标记节点为冲突状态
     */
    private void markNodesAsConflicted(IDagNode node1, IDagNode node2) {
        if (node1.getStatus() != NodeStatus.CONFLICTED) {
            dagGraph.updateNodeStatus(node1.getId(), NodeStatus.CONFLICTED);
        }
        if (node2.getStatus() != NodeStatus.CONFLICTED) {
            dagGraph.updateNodeStatus(node2.getId(), NodeStatus.CONFLICTED);
        }
    }
    
    /**
     * 检查节点是否处于活跃状态
     */
    private boolean isNodeActive(IDagNode node) {
        NodeStatus status = node.getStatus();
        return status == NodeStatus.READY_FOR_EXECUTION ||
               status == NodeStatus.EXECUTION_IN_PROGRESS ||
               status == NodeStatus.RESOLVED ||
               status == NodeStatus.COMPLETED;
    }

    /**
         * 冲突对，用作Map的键
         */
        private record ConflictPair(String nodeId1, String nodeId2) {
            private ConflictPair(String nodeId1, String nodeId2) {
                // 确保相同的两个节点总是产生相同的键，无论顺序如何
                if (nodeId1.compareTo(nodeId2) <= 0) {
                    this.nodeId1 = nodeId1;
                    this.nodeId2 = nodeId2;
                } else {
                    this.nodeId1 = nodeId2;
                    this.nodeId2 = nodeId1;
                }
            }

            @Override
            public String toString() {
                return nodeId1 + " <-> " + nodeId2;
            }
        }
} 