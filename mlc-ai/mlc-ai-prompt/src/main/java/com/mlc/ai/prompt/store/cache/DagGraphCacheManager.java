package com.mlc.ai.prompt.store.cache;

import com.mlc.ai.prompt.store.dag.DagGraph;
import io.nop.commons.cache.CacheConfig;
import io.nop.commons.cache.ICache;
import io.nop.commons.cache.LocalCache;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

/**
 * DagGraph缓存管理器
 * 提供DagGraph实例的缓存管理功能，支持按执行ID进行缓存和清理
 * 新增：管理节点执行输出的缓存
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DagGraphCacheManager {

    public static final DagGraphCacheManager INSTANCE = new DagGraphCacheManager();

    /**
     * DagGraph缓存实例,支持跨任务共享
     */
    private static final ICache<String, AtomicReference<DagGraph>> DAG_GRAPH_CACHE =
        LocalCache.newCache("dag-graph-cache", CacheConfig.newConfig(10000), s -> new AtomicReference<>());

    /**
     * 节点输出缓存
     * 键: executionId
     * 值: 另一个Map，其键是nodeId，值是该节点execute()方法返回的 Map<String, Object>
     * 使用 ConcurrentHashMap 来支持并发访问内部的nodeId -> output映射
     */
    private static final ICache<String, Map<String, Map<String, Object>>> NODE_OUTPUT_CACHE =
        LocalCache.newCache("node-output-cache", CacheConfig.newConfig(10000), s -> new ConcurrentHashMap<>());

    /**
     * 获取或创建DagGraph实例
     * 
     * @param executionId 执行ID
     * @return DagGraph实例
     */
    public DagGraph getOrCreateDagGraph(String executionId) {
        String cacheKey = generateCacheKey(executionId);
        
        return DAG_GRAPH_CACHE.get(cacheKey).updateAndGet(dagGraphRef -> {
            if (dagGraphRef == null) {
                DagGraph newDagGraph = new DagGraph();
                log.info("创建新的DagGraph实例，执行ID: {}, 缓存键: {}", executionId, cacheKey);
                return newDagGraph;
            }
            return dagGraphRef;
        });
    }

    /**
     * 获取现有的DagGraph实例
     * 
     * @param executionId 执行ID
     * @return DagGraph实例，如果不存在则返回null
     */
    public DagGraph getDagGraph(String executionId) {
        String cacheKey = generateCacheKey(executionId);
        AtomicReference<DagGraph> dagGraphRef = DAG_GRAPH_CACHE.get(cacheKey);
        return dagGraphRef != null ? dagGraphRef.get() : null;
    }

    /**
     * 存储DagGraph到缓存
     * 
     * @param executionId 执行ID
     * @param dagGraph DagGraph实例
     */
    public void putDagGraph(String executionId, DagGraph dagGraph) {
        String cacheKey = generateCacheKey(executionId);
        DAG_GRAPH_CACHE.put(cacheKey, new AtomicReference<>(dagGraph));
        log.info("DagGraph已存储到缓存，执行ID: {}, 缓存键: {}", executionId, cacheKey);
    }

    /**
     * 清理特定执行ID的DagGraph缓存和节点输出缓存
     * 
     * @param executionId 执行ID
     */
    public void clearCache(String executionId) {
        String dagGraphCacheKey = generateCacheKey(executionId);
        DAG_GRAPH_CACHE.remove(dagGraphCacheKey);
        log.info("清理DagGraph缓存，执行ID: {}, 缓存键: {}", executionId, dagGraphCacheKey);

        String nodeOutputCacheKey = generateNodeOutputExecutionCacheKey(executionId);
        NODE_OUTPUT_CACHE.remove(nodeOutputCacheKey);
        log.info("清理节点输出缓存，执行ID: {}, 缓存键: {}", executionId, nodeOutputCacheKey);
    }

    /**
     * 检查缓存中是否存在指定执行ID的DagGraph
     * 
     * @param executionId 执行ID
     * @return 如果存在返回true，否则返回false
     */
    public boolean containsCache(String executionId) {
        String cacheKey = generateCacheKey(executionId);
        return DAG_GRAPH_CACHE.get(cacheKey) != null;
    }

    /**
     * 生成缓存键
     * 
     * @param executionId 执行ID
     * @return 缓存键
     */
    private String generateCacheKey(String executionId) {
        return "dag_graph_" + executionId;
    }

    // ==================== 节点输出缓存方法 ====================

    /**
     * 生成节点输出缓存的执行ID级别键
     *
     * @param executionId 执行ID
     * @return 缓存键
     */
    private String generateNodeOutputExecutionCacheKey(String executionId) {
        return "node_output_" + executionId;
    }


    /**
     * 存储节点执行的输出结果
     * 
     * @param executionId 执行ID
     * @param nodeId 节点ID
     * @param output 节点执行的输出Map
     */
    public void putNodeOutput(String executionId, String nodeId, Map<String, Object> output) {
        if (output == null) {
            log.warn("尝试为 executionId: {}, nodeId: {} 存储null输出，将忽略。", executionId, nodeId);
            return;
        }
        String executionCacheKey = generateNodeOutputExecutionCacheKey(executionId);
        Map<String, Map<String, Object>> executionOutputs = NODE_OUTPUT_CACHE.get(executionCacheKey);
        if (executionOutputs == null) {
            executionOutputs = NODE_OUTPUT_CACHE.get(executionCacheKey);
        }

        executionOutputs.put(nodeId, output);
        log.debug("节点输出已缓存, executionId: {}, nodeId: {}, 缓存键: {}", executionId, nodeId, executionCacheKey);
    }

    /**
     * 获取缓存的节点输出结果
     * 
     * @param executionId 执行ID
     * @param nodeId 节点ID
     * @return 包含节点输出的Optional，如果未找到则为空Optional
     */
    public Optional<Map<String, Object>> getNodeOutput(String executionId, String nodeId) {
        String executionCacheKey = generateNodeOutputExecutionCacheKey(executionId);
        Map<String, Map<String, Object>> executionOutputs = NODE_OUTPUT_CACHE.get(executionCacheKey);

        if (executionOutputs != null) {
            return Optional.ofNullable(executionOutputs.get(nodeId));
        }
        return Optional.empty();
    }

    /**
     * 清理特定节点在一个执行中的输出缓存
     * 
     * @param executionId 执行ID
     * @param nodeId 节点ID
     */
    public void clearNodeOutput(String executionId, String nodeId) {
        String executionCacheKey = generateNodeOutputExecutionCacheKey(executionId);
        Map<String, Map<String, Object>> executionOutputs = NODE_OUTPUT_CACHE.get(executionCacheKey);
        if (executionOutputs != null) {
            if (executionOutputs.remove(nodeId) != null) {
                log.info("已清理节点输出缓存, executionId: {}, nodeId: {}, 缓存键: {}", executionId, nodeId, executionCacheKey);
            }
        }
    }
} 