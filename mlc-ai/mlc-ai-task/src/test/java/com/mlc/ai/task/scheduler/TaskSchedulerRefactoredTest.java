package com.mlc.ai.task.scheduler;

import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.engine.DagInMemoryEngine;
import com.mlc.ai.task.scheduler.manager.TaskStatusStrategyManager;
import com.mlc.ai.task.scheduler.policy.DefaultTaskSchedulerPolicy;
import com.mlc.ai.task.scheduler.policy.ITaskSchedulerPolicy;
import com.mlc.ai.task.scheduler.status.SchedulerStatus;
import com.mlc.ai.task.workflow.WorkflowEngine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import reactor.core.publisher.Flux;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * TaskScheduler 重构后的测试类
 * 验证重构后的组件是否正常工作
 */
class TaskSchedulerRefactoredTest {

    @Mock
    private DagInMemoryEngine dagEngine;

    @Mock
    private WorkflowEngine workflowEngine;

    @Mock
    private ExecutionContext executionContext;

    private TaskScheduler taskScheduler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 模拟执行上下文
        when(executionContext.getExecutionId()).thenReturn("test-execution-id");
        when(executionContext.getStatusStrategyManager()).thenReturn(mock(TaskStatusStrategyManager.class));
        
        // 模拟DAG引擎
        when(dagEngine.isValid()).thenReturn(true);
        when(dagEngine.getName()).thenReturn("test-dag");
        when(dagEngine.getEntryTasks()).thenReturn(java.util.Set.of());
        when(dagEngine.getTasks()).thenReturn(java.util.Map.of());
    }

//    @Test
//    void testCreateDefaultScheduler() {
//        // 使用工厂创建默认调度器
//        taskScheduler = TaskSchedulerFactory.createDefault(dagEngine, workflowEngine, executionContext);
//
//        assertNotNull(taskScheduler);
//        assertNotNull(taskScheduler.getSchedulerId());
//        assertEquals(SchedulerStatus.IDLE, taskScheduler.getStatus());
//    }
//
//    @Test
//    void testCreateHighConcurrencyScheduler() {
//        // 创建高并发调度器
//        taskScheduler = TaskSchedulerFactory.createHighConcurrency(dagEngine, workflowEngine, executionContext, 20);
//
//        assertNotNull(taskScheduler);
//        assertEquals(SchedulerStatus.IDLE, taskScheduler.getStatus());
//    }
//
//    @Test
//    void testCreateSerialScheduler() {
//        // 创建串行调度器
//        taskScheduler = TaskSchedulerFactory.createSerial(dagEngine, workflowEngine, executionContext);
//
//        assertNotNull(taskScheduler);
//        assertEquals(SchedulerStatus.IDLE, taskScheduler.getStatus());
//    }
//
//    @Test
//    void testCreateFailFastScheduler() {
//        // 创建快速失败调度器
//        taskScheduler = TaskSchedulerFactory.createFailFast(dagEngine, workflowEngine, executionContext);
//
//        assertNotNull(taskScheduler);
//        assertEquals(SchedulerStatus.IDLE, taskScheduler.getStatus());
//    }
//
//    @Test
//    void testCreateCustomScheduler() {
//        // 创建自定义配置调度器
//        taskScheduler = TaskSchedulerFactory.createCustom(dagEngine, workflowEngine, executionContext, 5, false);
//
//        assertNotNull(taskScheduler);
//        assertEquals(SchedulerStatus.IDLE, taskScheduler.getStatus());
//    }
//
//    @Test
//    void testCreateWithCustomPolicy() {
//        // 创建自定义策略
//        ITaskSchedulerPolicy customPolicy = DefaultTaskSchedulerPolicy.builder()
//                .maxParallelism(8)
//                .continueOnFailure(true)
//                .build();
//
//        taskScheduler = TaskSchedulerFactory.createWithPolicy(dagEngine, workflowEngine, executionContext, customPolicy);
//
//        assertNotNull(taskScheduler);
//        assertEquals(SchedulerStatus.IDLE, taskScheduler.getStatus());
//    }
//
//    @Test
//    void testSchedulerStateTransitions() {
//        taskScheduler = TaskSchedulerFactory.createDefault(dagEngine, workflowEngine, executionContext);
//
//        // 初始状态应该是IDLE
//        assertEquals(SchedulerStatus.IDLE, taskScheduler.getStatus());
//
//        // 测试取消操作
//        taskScheduler.cancel();
//        // 由于不在运行状态，状态不应该改变
//        assertEquals(SchedulerStatus.IDLE, taskScheduler.getStatus());
//
//        // 测试设置失败状态
//        taskScheduler.setFailed();
//        // 由于不在运行状态，状态不应该改变
//        assertEquals(SchedulerStatus.IDLE, taskScheduler.getStatus());
//    }
//
//    @Test
//    void testRegisterCompletionCallback() {
//        taskScheduler = TaskSchedulerFactory.createDefault(dagEngine, workflowEngine, executionContext);
//
//        boolean[] callbackExecuted = {false};
//
//        // 注册完成回调
//        taskScheduler.registerComplete(() -> {
//            callbackExecuted[0] = true;
//        });
//
//        // 验证回调已注册（通过检查状态管理器）
//        assertNotNull(taskScheduler.getSchedulerStatusManager());
//        assertFalse(callbackExecuted[0]); // 回调还未执行
//    }
//
//    @Test
//    void testGetSchedulerStats() {
//        taskScheduler = TaskSchedulerFactory.createDefault(dagEngine, workflowEngine, executionContext);
//
//        String stats = taskScheduler.getSchedulerStats();
//
//        assertNotNull(stats);
//        assertTrue(stats.contains("调度器状态"));
//        assertTrue(stats.contains("队列大小"));
//        assertTrue(stats.contains("任务状态统计"));
//    }
//
//    @Test
//    void testManagerComponents() {
//        taskScheduler = TaskSchedulerFactory.createDefault(dagEngine, workflowEngine, executionContext);
//
//        // 验证管理器组件都已正确初始化
//        assertNotNull(taskScheduler.getSchedulerStatusManager());
//        assertNotNull(taskScheduler.getQueueManager());
//        assertNotNull(taskScheduler.getDependencyChecker());
//
//        // 验证状态管理器的初始状态
//        assertEquals(SchedulerStatus.IDLE, taskScheduler.getSchedulerStatusManager().getCurrentStatus());
//        assertFalse(taskScheduler.getSchedulerStatusManager().getSchedulingCompleted());
//        assertTrue(taskScheduler.getSchedulerStatusManager().canStart());
//
//        // 验证队列管理器的初始状态
//        assertTrue(taskScheduler.getQueueManager().isQueueEmpty());
//        assertEquals(0, taskScheduler.getQueueManager().getRunningTaskCount());
//        assertTrue(taskScheduler.getQueueManager().isIdle());
//    }
//
//    @Test
//    void testInvalidDAG() {
//        // 模拟无效的DAG
//        when(dagEngine.isValid()).thenReturn(false);
//
//        taskScheduler = TaskSchedulerFactory.createDefault(dagEngine, workflowEngine, executionContext);
//
//        // 启动调度器应该失败
//        Flux<String> result = taskScheduler.start();
//
//        // 验证返回的是错误流
//        assertNotNull(result);
//
//        // 尝试订阅并捕获异常
//        try {
//            result.blockFirst();
//            fail("应该抛出异常");
//        } catch (Exception e) {
//            assertTrue(e instanceof IllegalArgumentException);
//        }
//    }
//
//    @Test
//    void testSchedulerAlreadyRunning() {
//        taskScheduler = TaskSchedulerFactory.createDefault(dagEngine, workflowEngine, executionContext);
//
//        // 手动设置状态为运行中
//        taskScheduler.getSchedulerStatusManager().changeStatus(SchedulerStatus.RUNNING);
//
//        // 再次启动应该失败
//        Flux<String> result = taskScheduler.start();
//
//        // 验证返回的是错误流
//        assertNotNull(result);
//
//        // 尝试订阅并捕获异常
//        try {
//            result.blockFirst();
//            fail("应该抛出异常");
//        } catch (Exception e) {
//            assertTrue(e instanceof IllegalStateException);
//        }
//    }
}
