package com.mlc.ai.task.context;

import com.google.common.base.Strings;
import com.mlc.ai.task.scheduler.policy.DefaultTaskSchedulerPolicy;
import com.mlc.ai.task.scheduler.policy.ITaskSchedulerPolicy;
import com.mlc.base.common.utils.ContextKey;
import com.mlc.ai.task.executor.TaskExecutorRegistry;
import com.mlc.ai.task.executor.ITaskExecutor;
import com.mlc.ai.task.scheduler.manager.TaskStatusStrategyManager;
import com.mlc.ai.task.scheduler.strategy.ITaskStatusStrategy;
import java.util.Map;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 执行上下文, 在 Workflow 执行期间传递
 * 包含工作流执行过程中的所有状态和数据
 * <p>
 * 作为工作流执行的统一上下文入口，提供：
 * 1. 属性存储和访问
 * 2. 任务执行器管理
 * 3. 任务状态策略管理
 */
@Getter
@Setter
public class ExecutionContext {
    /**
     * 执行上下文唯一标识
     */
    private String executionId;
    
    /**
     * 通用上下文属性
     */
    private final Map<ContextKey<?>, Object> attributes = new ConcurrentHashMap<>();

    /**
     * 任务执行器注册表
     */
    private final TaskExecutorRegistry executorRegistry;
    
    /**
     * 任务状态策略管理器
     */
    private final TaskStatusStrategyManager statusStrategyManager;

    /**
     * 任务调度策略
     */
    private ITaskSchedulerPolicy taskSchedulerPolicy;

    /**
     * 是否发生错误
     */
    @Deprecated
    private AtomicBoolean errorOccurred = new AtomicBoolean(false);

    /**
     * 构建器模式，用于创建 ExecutionContext 实例
     *
     * @param executionId 执行上下文的唯一标识
     * @param executorRegistry 任务执行器注册表
     * @param statusStrategyManager 任务状态策略管理器
     */
    public ExecutionContext(String executionId, TaskExecutorRegistry executorRegistry, TaskStatusStrategyManager statusStrategyManager,
                            ITaskSchedulerPolicy taskSchedulerPolicy) {
        this.executionId = executionId;
        this.executorRegistry = executorRegistry;
        this.statusStrategyManager = statusStrategyManager;
        this.taskSchedulerPolicy = taskSchedulerPolicy;
    }

    // 创建默认工厂方法
    public static ExecutionContext getInstance(){
        return new ExecutionContext(
            UUID.randomUUID().toString(),
            new TaskExecutorRegistry(),
            new TaskStatusStrategyManager(),
            DefaultTaskSchedulerPolicy.builder().build()
        );
    }


    /**
     * 获取类型安全的上下文属性。
     *
     * @param key 类型安全的键
     * @param <T> 属性类型
     * @return 属性值，如果不存在则返回 null。调用者负责处理 null 情况。
     * @throws ClassCastException 如果存储的值与键的类型不匹配（理论上不应发生如果 setAttribute 被正确使用）
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(ContextKey<T> key) {
        return (T) attributes.get(key);
    }

    /**
     * 设置类型安全的上下文属性。
     *
     * @param key 类型安全的键
     * @param value 要存储的值
     * @param <T> 属性类型
     */
    public <T> void setAttribute(ContextKey<T> key, T value) {
        attributes.put(key, value);
    }
    
    /**
     * 注册任务执行器
     * 
     * @param executorKey 执行器键
     * @param executor 执行器实例
     */
    public void registerExecutor(String executorKey, ITaskExecutor executor) {
        assert !Strings.isNullOrEmpty(executorKey) : "执行器键不能为空";
        assert executor != null : "执行器实例不能为空";
        assert executorRegistry != null : "执行器注册表不能为空";
        executorRegistry.registerExecutor(executorKey, executor);
    }

    /**
     * 添加状态处理策略
     *
     * @param strategy 要添加的策略
     */
    public void addStatusStrategy(ITaskStatusStrategy strategy) {
        assert strategy != null : "策略不能为空";
        assert statusStrategyManager != null : "状态策略管理器不能为空";
        statusStrategyManager.addStrategy(strategy);
    }

}