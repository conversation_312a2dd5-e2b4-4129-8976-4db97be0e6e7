package com.mlc.ai.task.workflow;

import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.engine.ITaskGraph;
import com.mlc.ai.task.executor.ITaskExecutor;
import com.mlc.ai.task.executor.TaskExecutorDecorator;
import com.mlc.ai.task.executor.TaskExecutorRegistry;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import com.mlc.ai.task.model.basic.BaseTaskModel.TaskStatus;
import com.mlc.ai.task.model.basic.TaskContext;
import com.mlc.ai.task.scheduler.TaskScheduler;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeoutException;

/**
 * 工作流引擎
 * 负责：
 * 1. 协调 DAG引擎和任务调度器
 * 2. 管理工作流的执行生命周期
 * 3. 执行具体任务
 * 4. 处理任务超时和重试
 * 5. 管理任务执行状态
 */
@Slf4j
@Getter
public class WorkflowEngine {

    /**
     * 工作流引擎ID
     */
    private final String engineId = UUID.randomUUID().toString();

    /**
     * DAG引擎
     */
    private final ITaskGraph dagEngine;

    /**
     * 执行上下文
     */
    private final ExecutionContext executionContext;

    /**
     * 任务调度器
     */
    private final TaskScheduler taskScheduler;

    /**
     * 初始化工作流引擎
     * 使用包含执行器注册表和状态策略管理器的完整执行上下文
     * 
     * @param dagEngine DAG引擎
     * @param executionContext 完整的执行上下文（包含执行器注册表和状态策略管理器）
     */
    public WorkflowEngine(ITaskGraph dagEngine, ExecutionContext executionContext) {
        if (executionContext.getExecutorRegistry() == null) {
            throw new IllegalArgumentException("执行上下文中缺少任务执行器注册表");
        }
        if (executionContext.getStatusStrategyManager() == null) {
            throw new IllegalArgumentException("执行上下文中缺少任务状态策略管理器");
        }

        this.dagEngine = dagEngine;
        this.executionContext = executionContext;

        // 创建任务调度器
        this.taskScheduler = new TaskScheduler(this);
    }


    /**
     * 启动工作流执行
     * 
     * @return 执行结果流
     */
    public Flux<String> start() {
        log.info("[{}] 启动工作流: {}", executionContext.getExecutionId(), dagEngine.getName());
        return taskScheduler.start();
    }

    /**
     * 取消工作流执行
     */
    public void cancel() {
        if (taskScheduler != null) {
            taskScheduler.cancel();
            log.info("[{}] 取消工作流执行: {}", executionContext.getExecutionId(), dagEngine.getName());
        }
    }
    
    /**
     * 异步执行单个任务
     * 
     * @param task 要执行的任务
     * @return 执行结果流
     */
    public Flux<String> executeTask(BaseTaskModel task) {
        String taskId = task.getId();
        
        log.info("[{}] 开始执行任务: {} (ID: {})", executionContext.getExecutionId(), task.getName(), taskId);
        
        // 获取任务执行器
        String executorKey = task.getExecutorKey();
        if (executorKey == null || executorKey.isEmpty()) {
            log.error("[{}] 任务未指定执行器: {}", executionContext.getExecutionId(), taskId);
            return Flux.error(new IllegalStateException("任务未指定执行器"));
        }
        
        // 优先使用执行上下文中的执行器注册表，如果没有则使用实例变量
        TaskExecutorRegistry currentRegistry = executionContext.getExecutorRegistry();
        
        ITaskExecutor executor = currentRegistry.getExecutor(executorKey);
        if (executor == null) {
            log.error("[{}] 找不到任务执行器: {} -> {}", executionContext.getExecutionId(), taskId, executorKey);
            return Flux.error(new IllegalStateException("找不到任务执行器: " + executorKey));
        }

        // 使用装饰器包装执行器，处理开始和错误消息
        ITaskExecutor decoratedExecutor = new TaskExecutorDecorator(executor);

        log.info("[{}] 执行任务 {} 使用执行器: {}", executionContext.getExecutionId(), taskId, executorKey);
        
        // 更新任务开始执行
        dagEngine.notifyStatusChange(taskId, TaskStatus.PENDING, TaskStatus.RUNNING);
        
        // 预处理任务上下文：填充上游和下游任务关系
        this.prepareTaskContext(task);

        long timeoutMs = task.getConfig().getTimeoutMs();
        int maxRetries = task.getConfig().getMaxRetries();

        // 使用 Flux.defer 延迟内部逻辑的执行直到订阅发生，确保每次订阅都重新调用 executor.execute()，生成全新的执行链路，避免状态污染。
        return Flux.defer(() ->
            decoratedExecutor.execute(task, executionContext)
            .doOnComplete(() -> {
                log.info("[{}] 任务执行成功: {}", executionContext.getExecutionId(), taskId);
                dagEngine.notifyStatusChange(taskId, TaskStatus.RUNNING, TaskStatus.COMPLETED);
            })
            .doOnError(error -> {
                log.error("[{}] 任务执行出错: {} - {}", executionContext.getExecutionId(), taskId, error.getMessage(), error);
                TaskStatus targetStatus = (error instanceof TimeoutException) ? TaskStatus.TIMEOUT : TaskStatus.FAILED;
                dagEngine.notifyStatusChange(taskId, TaskStatus.RUNNING, targetStatus);
            })
            .transform(flux -> timeoutMs > 0 ?
                flux.timeout(Duration.ofMillis(timeoutMs)) : flux
            )
            .transform(flux -> maxRetries > 0 ?
                flux.retryWhen(
                   Retry.fixedDelay(maxRetries, Duration.ofSeconds(3))
                        .filter(ex -> !(ex instanceof TimeoutException))
                        .doBeforeRetry(rs -> {
                            dagEngine.notifyStatusChange(taskId, TaskStatus.FAILED, TaskStatus.RUNNING);
                            log.warn("[{}] 重试任务 {}, 第 {} 次",
                                     executionContext.getExecutionId(), taskId, rs.totalRetries() + 1);
                        })
                ) : flux
            )
        );
    }
    
    /**
     * 预处理任务上下文
     * 填充上下游任务关系，为任务执行准备必要的上下文信息
     * 
     * @param task 要预处理的任务
     */
    private void prepareTaskContext(BaseTaskModel task) {
        String taskId = task.getId();
        TaskContext taskContext = task.getTaskContext();

        // 获取并设置上游任务
        Set<String> predecessorIds = dagEngine.getPredecessors(taskId);
        for (String predId : predecessorIds) {
            BaseTaskModel predTask = dagEngine.getTasks().get(predId);
            if (predTask != null) {
                taskContext.getUpstreamTasks().put(predId, predTask);
            }
        }

        // 合并上游任务输出到当前任务输入
        taskContext.mergeUpstreamOutputs();
        
        // 添加元数据
        taskContext.getMetadata().put("executionId", executionContext.getExecutionId());
        taskContext.getMetadata().put("taskId", taskId);
    }
} 