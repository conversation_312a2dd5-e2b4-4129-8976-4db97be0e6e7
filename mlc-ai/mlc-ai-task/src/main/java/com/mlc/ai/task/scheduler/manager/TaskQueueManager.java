package com.mlc.ai.task.scheduler.manager;

import com.mlc.ai.task.engine.ITaskGraph;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import com.mlc.ai.task.model.basic.BaseTaskModel.TaskStatus;
import com.mlc.ai.task.scheduler.policy.ITaskSchedulerPolicy;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 任务队列管理器
 * 负责管理准备执行的任务队列和运行中的任务计数
 */
@Slf4j
@Getter
public class TaskQueueManager {

    private final ITaskGraph dagEngine;

    public TaskQueueManager(ITaskGraph dagEngine) {
        this.dagEngine = dagEngine;
    }

    /**
     * 准备执行的任务队列，将所有入度为0的任务加入队列
     */
    private final Queue<String> readyTasksQueue = new ConcurrentLinkedQueue<>();

    /**
     * 执行中的任务数量
     */
    private final AtomicInteger runningTasks = new AtomicInteger(0);

    /**
     * 初始化队列，将所有入度为0的任务加入准备队列
     */
    public void initializeQueue() {
        Set<String> entryTasks = dagEngine.getEntryTasks();
        entryTasks.forEach(readyTasksQueue::offer);
        log.debug("初始化任务队列，加入 {} 个入口任务", entryTasks.size());
    }

    /**
     * 清空准备队列
     */
    public void clearQueue() {
        readyTasksQueue.clear();
        log.debug("清空任务准备队列");
    }

    /**
     * 添加任务到准备队列
     *
     * @param taskId 任务ID
     */
    public void addToQueue(String taskId) {
        readyTasksQueue.offer(taskId);
        log.debug("任务 {} 加入准备队列", taskId);
    }

    /**
     * 从准备队列中移除任务
     *
     * @param taskId 任务ID
     * @return 是否成功移除
     */
    public boolean removeFromQueue(String taskId) {
        boolean removed = readyTasksQueue.remove(taskId);
        if (removed) {
            log.debug("任务 {} 从准备队列中移除", taskId);
        }
        return removed;
    }

    /**
     * 检查准备队列是否为空
     *
     * @return 是否为空
     */
    public boolean isQueueEmpty() {
        return readyTasksQueue.isEmpty();
    }

    /**
     * 获取准备队列大小
     *
     * @return 队列大小
     */
    public int getQueueSize() {
        return readyTasksQueue.size();
    }

    /**
     * 获取运行中的任务数量
     *
     * @return 运行中的任务数量
     */
    public int getRunningTaskCount() {
        return runningTasks.get();
    }

    /**
     * 增加运行中的任务计数
     *
     * @return 增加后的计数
     */
    public int incrementRunningTasks() {
        return runningTasks.incrementAndGet();
    }

    /**
     * 减少运行中的任务计数
     *
     * @return 减少后的计数
     */
    public int decrementRunningTasks() {
        return runningTasks.decrementAndGet();
    }

    /**
     * 获取可执行的任务列表
     *
     * @param schedulerPolicy 调度策略
     * @param maxCount 最大数量
     * @return 可执行的任务列表
     */
    public List<BaseTaskModel> getExecutableTasks(ITaskSchedulerPolicy schedulerPolicy, int maxCount) {
        // 计算可以并行执行的任务数量
        int availableSlots = schedulerPolicy.getMaxParallelism() - runningTasks.get();
        int actualMaxCount = Math.min(maxCount, availableSlots);

        if (actualMaxCount <= 0) {
            return List.of();
        }

        // 将准备队列中的任务转为模型列表，用于策略选择
        List<BaseTaskModel> readyTasks = readyTasksQueue.stream()
                .map(taskId -> dagEngine.getTasks().get(taskId))
                .filter(task -> task != null && task.getStatus() == TaskStatus.PENDING)
                .limit(actualMaxCount)
                .collect(Collectors.toList());

        return readyTasks;
    }

    /**
     * 选择下一个要执行的任务
     *
     * @param schedulerPolicy 调度策略
     * @return 选中的任务，如果无法选择则返回null
     */
    public BaseTaskModel selectNextTask(ITaskSchedulerPolicy schedulerPolicy) {
        List<BaseTaskModel> readyTasks = getExecutableTasks(schedulerPolicy, 1);
        if (readyTasks.isEmpty()) {
            return null;
        }

        BaseTaskModel selectedTask = schedulerPolicy.selectNextTask(readyTasks);
        if (selectedTask != null) {
            // 从准备队列中移除已选择的任务
            removeFromQueue(selectedTask.getId());
            // 增加正在执行的任务计数
            incrementRunningTasks();
        }

        return selectedTask;
    }

    /**
     * 检查是否没有任务在执行且队列为空
     *
     * @return 是否空闲
     */
    public boolean isIdle() {
        return isQueueEmpty() && getRunningTaskCount() == 0;
    }

    /**
     * 获取队列状态信息
     *
     * @return 状态信息字符串
     */
    public String getStatusInfo() {
        return String.format("队列大小: %d, 运行中任务: %d", getQueueSize(), getRunningTaskCount());
    }
}
