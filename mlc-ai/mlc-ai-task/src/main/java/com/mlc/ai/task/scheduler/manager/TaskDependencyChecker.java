package com.mlc.ai.task.scheduler.manager;

import com.mlc.ai.task.engine.ITaskGraph;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import com.mlc.ai.task.model.basic.BaseTaskModel.TaskStatus;
import java.util.Map.Entry;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;
import java.util.List;
import java.util.ArrayList;

/**
 * 任务依赖检查器
 * 负责检查任务的依赖关系和后继任务的可执行性
 */
@Slf4j
public class TaskDependencyChecker {

    /**
     * DAG引擎引用
     */
    private final ITaskGraph dagEngine;

    /**
     * 任务队列管理器引用
     */
    private final TaskQueueManager queueManager;

    /**
     * 执行上下文ID，用于日志
     */
    private final String executionId;

    /**
     * 构造函数
     *
     * @param dagEngine DAG引擎
     * @param queueManager 任务队列管理器
     * @param executionId 执行上下文ID
     */
    public TaskDependencyChecker(ITaskGraph dagEngine, TaskQueueManager queueManager, String executionId) {
        this.dagEngine = dagEngine;
        this.queueManager = queueManager;
        this.executionId = executionId;
    }

    /**
     * 检查任务的后继任务是否可以执行
     *
     * @param taskId 已完成的任务ID
     * @return 可以执行的后继任务ID列表
     */
    public List<String> checkSuccessors(String taskId) {
        List<String> readySuccessors = new ArrayList<>();
        Set<String> successors = dagEngine.getSuccessors(taskId);
        
        for (String successorId : successors) {
            if (isTaskReadyToExecute(successorId)) {
                queueManager.addToQueue(successorId);
                readySuccessors.add(successorId);
                log.debug("[{}] 任务 {} 的所有前驱任务已完成，加入执行队列", executionId, successorId);
            }
        }
        
        return readySuccessors;
    }

    /**
     * 检查任务是否准备好执行
     *
     * @param taskId 任务ID
     * @return 是否准备好执行
     */
    public boolean isTaskReadyToExecute(String taskId) {
        // 检查后继任务对应的实例
        BaseTaskModel successorTask = dagEngine.getTasks().get(taskId);
        if (successorTask == null || successorTask.getStatus() != TaskStatus.PENDING) {
            return false;
        }

        // 检查所有前驱任务是否都已完成
        return areAllPredecessorsCompleted(taskId);
    }

    /**
     * 检查所有前驱任务是否都已完成
     *
     * @param taskId 任务ID
     * @return 是否所有前驱任务都已完成
     */
    public boolean areAllPredecessorsCompleted(String taskId) {
        Set<String> predecessors = dagEngine.getPredecessors(taskId);
        
        for (String predecessorId : predecessors) {
            BaseTaskModel predecessorTask = dagEngine.getTasks().get(predecessorId);
            if (predecessorTask == null) {
                continue;
            }

            // 检查前驱任务的状态，如果是PENDING或RUNNING，则不能执行后继任务
            TaskStatus predecessorStatus = predecessorTask.getStatus();
            if (predecessorStatus == TaskStatus.PENDING || predecessorStatus == TaskStatus.RUNNING) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 检查所有任务是否都已结束（成功、跳过、取消或失败）
     *
     * @return 是否所有任务都已结束
     */
    public boolean areAllTasksFinished() {
        return dagEngine.getTasks().values().stream()
                .allMatch(task -> {
                    TaskStatus status = task.getStatus();
                    return status == TaskStatus.COMPLETED
                        || status == TaskStatus.SKIPPED
                        || status == TaskStatus.CANCELLED
                        || status == TaskStatus.FAILED;
                });
    }

    /**
     * 检查是否有任务失败
     *
     * @return 是否有任务失败
     */
    public boolean hasFailedTasks() {
        return dagEngine.getTasks().values().stream()
                .anyMatch(task -> task.getStatus() == TaskStatus.FAILED);
    }

    /**
     * 获取指定状态的任务数量
     *
     * @param status 任务状态
     * @return 任务数量
     */
    public long getTaskCountByStatus(TaskStatus status) {
        return dagEngine.getTasks().values().stream()
                .filter(task -> task.getStatus() == status)
                .count();
    }

    /**
     * 获取所有运行中的任务ID
     *
     * @return 运行中的任务ID集合
     */
    public Set<String> getRunningTaskIds() {
        return dagEngine.getTasks().entrySet().stream()
                .filter(entry -> entry.getValue().getStatus() == TaskStatus.RUNNING)
                .map(Entry::getKey)
                .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 取消所有运行中的任务
     */
    public void cancelRunningTasks() {
        Set<String> runningTaskIds = getRunningTaskIds();
        for (String taskId : runningTaskIds) {
            dagEngine.notifyStatusChange(taskId, TaskStatus.RUNNING, TaskStatus.CANCELLED);
            log.debug("[{}] 取消运行中的任务: {}", executionId, taskId);
        }
    }

    /**
     * 获取依赖检查统计信息
     *
     * @return 统计信息字符串
     */
    public String getDependencyStats() {
        long pendingCount = getTaskCountByStatus(TaskStatus.PENDING);
        long runningCount = getTaskCountByStatus(TaskStatus.RUNNING);
        long completedCount = getTaskCountByStatus(TaskStatus.COMPLETED);
        long failedCount = getTaskCountByStatus(TaskStatus.FAILED);
        long skippedCount = getTaskCountByStatus(TaskStatus.SKIPPED);
        long cancelledCount = getTaskCountByStatus(TaskStatus.CANCELLED);
        
        return String.format("任务状态统计 - 待执行: %d, 运行中: %d, 已完成: %d, 已失败: %d, 已跳过: %d, 已取消: %d",
                pendingCount, runningCount, completedCount, failedCount, skippedCount, cancelledCount);
    }
}
