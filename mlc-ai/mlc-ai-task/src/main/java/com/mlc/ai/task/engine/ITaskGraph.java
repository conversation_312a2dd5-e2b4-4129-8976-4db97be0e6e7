package com.mlc.ai.task.engine;

import com.mlc.ai.task.model.basic.BaseTaskModel;

import com.mlc.ai.task.model.basic.BaseTaskModel.TaskStatus;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

/**
 * 任务图接口，定义任务依赖关系图的基本操作
 */
public interface ITaskGraph {

    /**
     * 获取DAG引擎ID
     *
     * @return 引擎ID
     */
    String getEngineId();

    /**
     * DAG引擎名称
     *
     * @return 引擎名称
     */
    String getName();

    /**
     * 添加任务
     *
     * @param task 要添加的任务
     * @return 任务ID
     */
    String addTask(BaseTaskModel task);
    
    /**
     * 添加任务依赖关系
     *
     * @param fromTaskId 前置任务ID
     * @param toTaskId 后置任务ID
     * @return 是否添加成功
     */
    boolean addDependency(String fromTaskId, String toTaskId);
    
    /**
     * 移除任务依赖关系
     *
     * @param fromTaskId 前置任务ID
     * @param toTaskId 后置任务ID
     * @return 是否移除成功
     */
    boolean removeDependency(String fromTaskId, String toTaskId);
    
    /**
     * 获取任务的前置任务
     *
     * @param taskId 任务ID
     * @return 前置任务ID集合
     */
    Set<String> getPredecessors(String taskId);
    
    /**
     * 获取任务的后置任务
     *
     * @param taskId 任务ID
     * @return 后置任务ID集合
     */
    Set<String> getSuccessors(String taskId);
    
    /**
     * 获取入度为0的任务（起始任务）
     *
     * @return 入度为0的任务ID集合
     */
    Set<String> getEntryTasks();
    
    /**
     * 获取出度为0的任务（终止任务）
     *
     * @return 出度为0的任务ID集合
     */
    Set<String> getExitTasks();
    
    /**
     * 验证任务图是否有效（无环）
     *
     * @return 如果有效返回true，否则返回false
     */
    boolean isValid();
    
    /**
     * 获取拓扑排序结果
     *
     * @return 按拓扑顺序排列的任务ID列表
     */
    List<String> getTopologicalOrder();
    
    /**
     * 获取从起点任务可达的所有任务
     *
     * @param startTaskId 起点任务ID
     * @return 可达任务ID集合
     */
    Set<String> getReachableTasks(String startTaskId);


    /**
     * 获取所有任务
     *
     * @return 任务ID到任务实例的映射
     */
    Map<String, BaseTaskModel> getTasks();

    /**
     * 添加状态变更监听器
     *
     * @param listener 状态变更监听器
     */
    void addStatusChangeListener(Consumer<TaskStatusEvent> listener);

    /**
     * 通知状态变更
     */
    void notifyStatusChange(String taskId, TaskStatus oldStatus, TaskStatus newStatus);

    /**
     * 动态添加任务
     *
     * @param task           要添加的任务
     * @param predecessorIds 前驱任务ID集合
     * @param successorIds   后继任务ID集合
     * @return 添加的任务ID
     */
    String dynamicAddTask(BaseTaskModel task, Set<String> predecessorIds, Set<String> successorIds);
} 