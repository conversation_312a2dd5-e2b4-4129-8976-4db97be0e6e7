package com.mlc.ai.task.scheduler.strategy;

import com.mlc.ai.task.engine.ITaskGraph;
import com.mlc.ai.task.engine.TaskStatusEvent;
import com.mlc.ai.task.model.basic.BaseTaskModel.TaskStatus;
import com.mlc.ai.task.scheduler.TaskScheduler;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 跳转到最终任务策略
 * 当任务失败时，将其所有后继任务（除了最终任务）标记为 CANCELLED
 * 并添加失败任务到最终任务的直接依赖
 */
@Slf4j
public class JumpToFinalTaskStrategy implements ITaskStatusStrategy {

    private final String finalTaskId;

    /**
     * 构造函数
     *
     * @param finalTaskId 最终任务ID，该任务不会被跳过，而是会在失败后执行
     */
    public JumpToFinalTaskStrategy(String finalTaskId) {
        this.finalTaskId = finalTaskId;
        log.info("创建JumpToFinalTaskStrategy, finalTaskId={}", finalTaskId);
    }

    @Override
    public void justHandle(TaskStatusEvent event, TaskScheduler context) {
        String taskId = event.getTaskId();
        ITaskGraph dagEngine = context.getDagEngine();

        log.info("执行跳过失败任务策略: {} (FAILED) -> 最终任务 {}", taskId, finalTaskId);
        
        // 1. 获取所有需要跳过的任务（失败任务的所有后继，除了最终任务和自身）
        Set<String> reachableTasks = dagEngine.getReachableTasks(taskId);
        Set<String> tasksToCancel = reachableTasks.stream()
                                                  .filter(id -> !id.equals(finalTaskId) && !id.equals(taskId))
                                                  .collect(Collectors.toSet());

        if (tasksToCancel.isEmpty()) {
            log.info("没有需要操作的任务");
            return;
        }
        log.debug("需要跳过的任务: {}", tasksToCancel);

        // 2. 找出失败任务和最终任务之间的所有中间任务，并将它们清理 removeDependency
        tasksToCancel.forEach(t -> {
            boolean removed = dagEngine.removeDependency(taskId, t);
            if (!removed) {
                log.warn("无法清除失败任务 {} 和任务 {} 之间的依赖关系", taskId, t);
            }
            dagEngine.notifyStatusChange(t, TaskStatus.PENDING, TaskStatus.CANCELLED);
        });

        // 3. 确保最终任务可以执行
        // 检查失败任务是否已经是最终任务的前置依赖
        Set<String> finalTaskPredecessors = dagEngine.getPredecessors(finalTaskId);
        finalTaskPredecessors.forEach(t -> {
            boolean removed = dagEngine.removeDependency(t, finalTaskId);
            if (!removed) {
                log.warn("无法清除任务 {} 和最终任务 {} 之间的依赖关系: {}", t, taskId, finalTaskId);
            }
        });
        if (!finalTaskPredecessors.contains(taskId)) {
            // 将失败任务添加为最终任务的直接依赖
            boolean added = dagEngine.addDependency(taskId, finalTaskId);
            if (!added) {
                log.warn("无法添加失败任务 {} 到最终任务 {} 的依赖", taskId, finalTaskId);
            }
        } else {
            log.debug("失败任务 {} 已经是最终任务 {} 的前置依赖", taskId, finalTaskId);
        }
    }

    @Override
    public boolean canHandle(TaskStatusEvent event) {
        // 只处理任务从RUNNING状态变为FAILED状态的情况
        return event.getOldStatus() == TaskStatus.RUNNING && event.getNewStatus() == TaskStatus.FAILED;
    }
} 