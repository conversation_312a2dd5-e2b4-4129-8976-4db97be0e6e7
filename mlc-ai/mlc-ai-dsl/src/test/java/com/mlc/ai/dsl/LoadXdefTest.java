package com.mlc.ai.dsl;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.nop.ai.core.model.PromptModel;
import io.nop.ai.core.xdef.AiXDefHelper;
import io.nop.api.core.ApiConfigs;
import io.nop.api.core.config.AppConfig;
import io.nop.core.initialize.CoreInitialization;
import io.nop.core.lang.json.JsonTool;
import io.nop.core.lang.xml.json.CompactXNodeToJsonTransformer;
import io.nop.core.unittest.BaseTestCase;
import io.nop.xlang.xdef.IXDefNode;
import io.nop.xlang.xdef.IXDefinition;
import io.nop.xlang.xmeta.IObjMeta;
import io.nop.xlang.xmeta.SchemaLoader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

@Slf4j
public class LoadXdefTest extends BaseTestCase {

    @BeforeAll
    public static void setUp() {
        AppConfig.getConfigProvider().updateConfigValue(ApiConfigs.CFG_EXCEPTION_FILL_STACKTRACE, true);
        CoreInitialization.initialize();
    }

    @AfterAll
    public static void tearDown() {
        CoreInitialization.destroy();
    }

    /**
     * 测试从 orm.xdef 文件加载模型定义
     * 该测试将解析 orm.xdef 文件，提取模型定义，并打印相关信息
     */
    @Test
    public void testLoadParseXdef() {
        log.info("信息：尝试从 orm.xdef 加载模型定义。");
        try {

            Map<String, Object> stringObjectMap = AiXDefHelper.loadXDefForAiAsJson("/nop/schema/orm/orm.xdef");
            System.out.println("XDef节点信息: " + new ObjectMapper().writeValueAsString(stringObjectMap));


            IXDefinition ixDefinition = SchemaLoader.loadXDefinition("/nop/schema/orm/orm.xdef");
            Object json = new CompactXNodeToJsonTransformer().transformToObject(ixDefinition.toNode());
            System.out.println(JsonTool.serialize(json, true));

            IObjMeta iObjMeta = SchemaLoader.loadXMeta("/nop/schema/orm/orm.xdef");
            System.out.println("XMeta节点信息: " + iObjMeta.toNode().jsonText());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void load() {
        final Map<String, PromptModel> definitions = new HashMap<>();

        // <ChildKey, List<ParentKey>>
         final Map<String, List<String>> parentMap = new HashMap<>();

        // <ParentKey, List<ChildKey>>
         final Map<String, List<String>> childrenMap = new HashMap<>();

         final Set<String> topLevelKeys = new HashSet<>();

        log.info("信息：尝试从 orm.xdef 加载模型定义。");
        try {
            // /nop/schema/orm/orm.xdef
            IXDefinition ixDefinition = SchemaLoader.loadXDefinition("/nop/schema/orm/orm.xdef");

            // 预先加载YAML中的name和template信息，以便后续填充
    //            Map<String, Map<String, Object>> yamlData = loadYamlData();
    //            if (yamlData == null || yamlData.isEmpty()) {
    //                log.warn("警告：未找到或无法解析 YAML 定义文件，将只加载结构关系但没有名称和模板。");
    //            } else {
    //                log.info("成功加载YAML数据，包含以下键: {}", yamlData.keySet());
    //            }

            // 从IXDefinition中加载根节点（orm）
            String rootKey = ixDefinition.getTagName();

            // 将根节点添加到定义和顶层Keys中
            // addDefinition(rootKey, yamlData);
            topLevelKeys.add(rootKey);
            log.info("添加根节点: {}", rootKey);

            // 使用广度优先搜索遍历XDef树结构
            Queue<KeyNodePair> queue = new LinkedList<>();
            // 首先将根节点的所有子节点加入队列
            Map<String, ? extends IXDefNode> rootChildren = ixDefinition.getChildren();

            if (rootChildren != null && !rootChildren.isEmpty()) {
                log.info("根节点 {} 有 {} 个子节点", rootKey, rootChildren.size());
                for (Map.Entry<String, ? extends IXDefNode> entry : rootChildren.entrySet()) {
                    String childKey = entry.getKey();
                    IXDefNode childNode = entry.getValue();
                    queue.add(new KeyNodePair(childKey, childNode));

                    // 添加父子关系
                    parentMap.computeIfAbsent(childKey, k -> new ArrayList<>()).add(rootKey);
                    childrenMap.computeIfAbsent(rootKey, k -> new ArrayList<>()).add(childKey);
                    log.debug("添加父子关系: {} -> {}", rootKey, childKey);
                }
            } else {
                log.warn("根节点 {} 没有子节点", rootKey);
            }

            // 广度优先遍历处理所有节点
            while (!queue.isEmpty()) {
                KeyNodePair pair = queue.poll();
                String currentKey = pair.key;
                IXDefNode currentNode = pair.node;

                // 将当前节点添加到定义中
                // addDefinition(currentKey, yamlData);

                // 处理该节点的子节点
                Map<String, ? extends IXDefNode> children = currentNode.getChildren();
                if (children != null && !children.isEmpty()) {
                    log.debug("节点 {} 有 {} 个子节点", currentKey, children.size());
                    for (Map.Entry<String, ? extends IXDefNode> entry : children.entrySet()) {
                        String childKey = entry.getKey();
                        IXDefNode childNode = entry.getValue();
                        queue.add(new KeyNodePair(childKey, childNode));

                        // 添加父子关系
                        parentMap.computeIfAbsent(childKey, k -> new ArrayList<>()).add(currentKey);
                        childrenMap.computeIfAbsent(currentKey, k -> new ArrayList<>()).add(childKey);
                        log.debug("添加父子关系: {} -> {}", currentKey, childKey);
                    }
                }
            }

            // 重新计算顶层Keys（没有父级的定义）
            topLevelKeys.clear();
            for (String key : definitions.keySet()) {
                if (!parentMap.containsKey(key)) {
                    topLevelKeys.add(key);
                }
            }

            log.info("成功从 orm.xdef 加载模型定义。总共 {} 个定义, 顶层节点: {}", definitions.size(), topLevelKeys);
            log.debug("父子关系映射: {}", parentMap);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static class KeyNodePair {
        String key;
        IXDefNode node;

        KeyNodePair(String key, IXDefNode node) {
            this.key = key;
            this.node = node;
        }
    }
}
