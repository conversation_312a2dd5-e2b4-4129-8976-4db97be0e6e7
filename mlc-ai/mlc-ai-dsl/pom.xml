<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>mlc-ai</artifactId>
    <groupId>flowweb-zhongzhi</groupId>
    <version>${revision}</version>
  </parent>

  <artifactId>mlc-ai-dsl</artifactId>

  <modelVersion>4.0.0</modelVersion>

  <dependencies>
    <dependency>
      <groupId>flowweb-zhongzhi</groupId>
      <artifactId>mlc-ai-task</artifactId>
    </dependency>

    <dependency>
      <groupId>flowweb-zhongzhi</groupId>
      <artifactId>mlc-ai-core</artifactId>
    </dependency>

    <dependency>
      <groupId>flowweb-zhongzhi</groupId>
      <artifactId>mlc-defs</artifactId>
    </dependency>

    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>