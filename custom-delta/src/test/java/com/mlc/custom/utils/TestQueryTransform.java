package com.mlc.custom.utils;

import static io.nop.api.core.beans.FilterBeans.and;
import static io.nop.api.core.beans.FilterBeans.or;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.nop.api.core.beans.FilterBeans;
import io.nop.api.core.beans.TreeBean;
import io.nop.api.core.beans.query.QueryBean;
import io.nop.core.lang.sql.SQL;
import io.nop.core.lang.xml.XNode;
import io.nop.core.lang.xml.json.StdJsonToXNodeTransformer;
import io.nop.orm.dao.DaoQueryHelper;
import java.util.Map;
import java.util.function.Function;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

@Disabled
public class TestQueryTransform {

    @Test
    public void spliceSubquery() {

        QueryBean queryBean = new QueryBean();
        queryBean.addFilter(FilterBeans.eq("userId", "12345"));

        TreeBean sqlFilter = new TreeBean();
        sqlFilter.setTagName("sql");
        sqlFilter.setAttr("value", SQL.begin().sql("o.orderNum > '100' ").end());
        queryBean.addFilter(and(sqlFilter));

        Function<TreeBean, Object> ojectFunction = treeBean -> {

            if (treeBean.getChildren().get(0).getAttr("name").equals("userId")) {
                treeBean.getChildren().get(0).setAttr("value", SQL.begin().sql("select a from b where c > '1000' ").end());
            }
            return treeBean;
        };
        queryBean.transformFilter(ojectFunction);
        System.out.println("queryBean::::" + queryBean.getFilter().toJsonObject());
    }


    @Test
    public void toSelectSql() throws JsonProcessingException {

        String condition = """
            {
              "$type": "and",
              "$body": [{
                "$type": "eq",
                "name": "AAAAAA",
                "value": "12345"
              }, {
                "$type": "or",
                "$body": [{
                  "$type": "eq",
                  "name": "BBBBBBB",
                  "value": "admin"
                }, {
                  "$type": "eq",
                  "name": "CCCCCCC",
                  "value": "admin"
                }]
              }, {
                "$type": "eq",
                "name": "VVVVVVV",
                "value": "12345"
              }]
            }
            """;
        Map<String, Object> map = new ObjectMapper().readValue(condition, Map.class);
        XNode xNode = StdJsonToXNodeTransformer.INSTANCE.transformMap(map);
        System.out.println(xNode.toTreeBean());

        SQL sourceName = DaoQueryHelper.queryToSelectObjectSql("MlcUopUser", xNode.toTreeBean().toQueryBean());
        System.out.println("sourceName:::::" + sourceName);
    }

    @Test
    public void testJsonDsl() {

//        String xml = "<dao:FindPage xpl:lib='/nop/orm/xlib/dao.xlib' offset='0' limit='10'> select o from NopAuthUser o where o.id= ${id}</dao:FindPage>";
//
//        XNode node = XNodeParser.instance().parseFromText(null, xml);
//        IEvalAction action = XLang.newCompileTool().allowUnregisteredScopeVar(true).compileTag(node);
//        IEvalScope scope = XLang.newEvalScope();
//        scope.setLocalValue("id", 1);
//
//        List<NopAuthUser> list = (List<NopAuthUser>) action.invoke(scope);
//        assertTrue(!list.get(0).getUserName().isEmpty());
    }

    @Test
    public void testNestingQueryBean() throws JsonProcessingException {

        TreeBean and = and(FilterBeans.eq("AAAAAA", "12345"),
                           or(FilterBeans.eq("BBBBBBB", "admin"), FilterBeans.eq("CCCCCCC", "admin")),
                           FilterBeans.eq("VVVVVVV", "12345"));
        Object jsonObject = and.toJsonObject();
        System.out.println("jsonObject::::" + new ObjectMapper().writeValueAsString(jsonObject));
        SQL mlcUopUser = DaoQueryHelper.queryToSelectObjectSql("MlcUopUser", and.toQueryBean());
        System.out.println("mlcUopUser::::" + mlcUopUser);
    }


}
