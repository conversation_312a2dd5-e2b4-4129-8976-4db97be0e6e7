package com.mlc.custom.utils;

import io.nop.api.core.beans.query.QueryBean;
import io.nop.core.lang.xml.XNode;
import io.nop.core.lang.xml.parse.XNodeParser;
import io.nop.core.reflect.bean.BeanTool;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
public class QueryBeanTest {

    @Test
    public void xmlToQueryBean() {
        String xml = """
             <query>
                            <sourceName>NopAuthUser</sourceName>
                            <fields>
                                <field name="groupId"/>
                                <field name="name"/>
                                <field owner="deptMappings" name="deptId" aggFunc="count" alias="deptCount"/>
                            </fields>
                            <filter>
                                <c:if test="${someCondition}">
                                    <eq name="status" value="${status}"/>
                                </c:if>
                            </filter>
                            <orderBy>
                                <field name="name" asc="true"/>
                            </orderBy>
                    </query>
            """;

        XNode node = XNodeParser.instance().parseFromText(null, xml);
        QueryBean o = BeanTool.buildBeanFromTreeBean(node, QueryBean.class);
        log.info(o.toString());
    }
}
