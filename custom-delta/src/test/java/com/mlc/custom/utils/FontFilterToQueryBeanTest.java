package com.mlc.custom.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.base.common.beans.filter.FrontViewFilterGroupBean;
import com.mlc.base.common.beans.filter.FrontViewFilterNormalBean;
import com.mlc.base.common.utils.convert.FilterConvertSqlContent;
import io.nop.api.core.beans.TreeBean;
import io.nop.core.lang.sql.SQL;
import io.nop.core.lang.xml.XNode;
import io.nop.orm.dao.DaoQueryHelper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

@Slf4j
public class FontFilterToQueryBeanTest {

    private static final String TYPE_A = """
        [{
                "controlId": "f0770abbc24148579176254487a2c21a",
                    "dataType": 2,
                    "spliceType": 2,
                    "filterType": 2,
                    "isDynamicsource": false,
                    "dynamicSource": [],
                "values": ["wwwww"],
                "conditionGroupType": 1,
                    "type": 2
            }, {
                "controlId": "d6be0cdfaa1841f48b7f24808c3ae690",
                    "dataType": 2,
                    "spliceType": 2,
                    "filterType": 1,
                    "dateRange": 0,
                    "dateRangeType": 1,
                    "isDynamicsource": false,
                    "dynamicSource": [],
                "values": ["ddddwww"],
                "conditionGroupType": 1,
                    "type": 1
            }, {
                "controlId": "c84083555cc147e8af85ea99f474c501",
                    "dataType": 2,
                    "spliceType": 2,
                    "filterType": 3,
                    "dateRange": 0,
                    "dateRangeType": 1,
                    "isDynamicsource": false,
                    "dynamicSource": [],
                "values": ["eewqeqw"],
                "conditionGroupType": 1,
                    "type": 3
            }]
        """;

    private static final String TYPE_B = """
        
        [{
        	"spliceType": 2,
        	"isGroup": true,
        	"groupFilters": [{
        		"controlId": "ag",
        		"dataType": 2,
        		"spliceType": 2,
        		"filterType": 1,
        		"dateRange": 0,
        		"dateRangeType": 1,
        		"isDynamicsource": false,
        		"dynamicSource": [],
        		"values": ["3333", "333322"],
        		"conditionGroupType": 1,
        		"type": 2
        	}, {
        		"controlId": "ag",
        		"dataType": 2,
        		"spliceType": 2,
        		"filterType": 2,
        		"isDynamicsource": false,
        		"dynamicSource": [],
        		"values": ["312312312"],
        		"conditionGroupType": 1,
        		"type": 2
        	}]
        }, {
        	"spliceType": 2,
        	"isGroup": true,
        	"groupFilters": [{
        		"controlId": "name",
        		"dataType": 2,
        		"spliceType": 1,
        		"filterType": 2,
        		"isDynamicsource": false,
        		"dynamicSource": [],
        		"values": ["renren"],
        		"conditionGroupType": 1,
        		"type": 2
        	}]
        }]
        """;

//    @Test
//    public void nestingFilterTest() throws JsonProcessingException {
//
//        String testStr = TYPE_A;
//        boolean groupFilters = testStr.contains("groupFilters");
//        TreeBean result;
//        List<FrontViewFilterNormalBean> conditions = new ObjectMapper().readValue(testStr, new TypeReference<>() {
//        });
//        if (groupFilters) {
//            result = convertGroup(conditions);
//        } else {
//            result = convertUsually(conditions);
//        }
//        validateTreeBean(result);
//    }
//
//    private static TreeBean convertGroup(List<FrontViewFilterGroupBean> conditions) {
//        TreeBean treeBean = new TreeBean(conditions.get(0).getSpliceTypeStr());
//        conditions.forEach(group -> treeBean.addChild(convertUsually(group.getGroupFilters())));
//        return treeBean;
//    }
//
//    private static TreeBean convertUsually(List<FrontViewFilterNormalBean> conditions) {
//        List<TreeBean> list = conditions.stream()
//                                        .map(FilterConvertSqlContent.INSTANCE::convertSqlBody)
//                                        .toList();
//        TreeBean treeBean = new TreeBean(conditions.get(0).getSpliceTypeStr());
//        list.forEach(treeBean::addChild);
//        return treeBean;
//    }
//
//    public void validateTreeBean(TreeBean result) throws JsonProcessingException {
////        String value = new ObjectMapper().writeValueAsString(result);
////        System.out.println("value--------" + value);
//
//        log.info("xml----------- \n{}", XNode.fromTreeBean(result).xml());
//
//        log.info("treeBean-------- \n{}", new ObjectMapper().writeValueAsString(result.toJsonObject()));
//
//        SQL mlcUopUser = DaoQueryHelper.queryToSelectObjectSql("MlcUopUser", result.toQueryBean());
//        log.info("sql------ \n{}", mlcUopUser);
//    }
}
