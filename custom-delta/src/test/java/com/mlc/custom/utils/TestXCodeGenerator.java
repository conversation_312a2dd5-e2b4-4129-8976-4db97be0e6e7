package com.mlc.custom.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import io.nop.api.core.ApiConfigs;
import io.nop.api.core.config.AppConfig;
import io.nop.codegen.XCodeGenerator;
import io.nop.core.initialize.CoreInitialization;
import io.nop.core.lang.eval.IEvalScope;
import io.nop.core.resource.IFile;
import io.nop.core.resource.ResourceHelper;
import io.nop.core.resource.tpl.TemplateGenPath;
import io.nop.core.unittest.BaseTestCase;
import io.nop.xlang.api.XLang;
import java.time.LocalDateTime;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

public class TestXCodeGenerator extends BaseTestCase {
    @BeforeAll
    public static void setUp() {
        AppConfig.getConfigProvider().updateConfigValue(ApiConfigs.CFG_EXCEPTION_FILL_STACKTRACE, true);
        System.out.println("setUp");
        CoreInitialization.initialize();
    }

    @AfterAll
    public static void tearDown() {
        CoreInitialization.destroy();
    }

    @Test
    public void testSubDir() {
        IFile targetDir = getTargetResource("/codegen");
        ResourceHelper.deleteAll(targetDir);
        XCodeGenerator gen = new XCodeGenerator("/test/tpls", targetDir.getStdPath());

        IEvalScope scope = XLang.newEvalScope();
        LocalDateTime time = LocalDateTime.now();
        scope.setLocalValue(null, "currentTime", time);
        scope.setLocalValue(null, "forInt", new int[]{1111, 22222, 3333});

        gen.execute("/{child}", scope);
    }

    @Test
    public void testCopy() {
        IFile targetDir = getTargetResource("/codegen");
        ResourceHelper.deleteAll(targetDir);
        XCodeGenerator gen = new XCodeGenerator("/test/tpls", targetDir.getStdPath());

        IEvalScope scope = XLang.newEvalScope();
        LocalDateTime time = LocalDateTime.now();
        scope.setLocalValue(null, "currentTime", time);


        gen.execute("/", scope);

        assertTrue(targetDir.getResource("other").exists());
        assertTrue(!targetDir.getResource("@init.xrun").exists());
        assertEquals("other", ResourceHelper.readText(targetDir.getResource("other/other.txt")));
    }

    @Test
    public void testGenText() {
        IFile targetDir = getTargetResource("/codegen");
        ResourceHelper.deleteAll(targetDir);
        XCodeGenerator gen = new XCodeGenerator("/test/gen", targetDir.getStdPath());

        IEvalScope scope = XLang.newEvalScope();
        scope.setLocalValue(null, "moduleName", "aa");

        gen.execute("/", scope);

        String text = ResourceHelper.readText(targetDir.getResource("web.i18n.yaml"));
        assertEquals("\n" +
                "\"x:extends\": _aa-web.i18n.yaml\n" +
                "\n" +
                "# key: \"value\"\n", normalizeCRLF(text));
    }

    @Test
    public void testGenPath() {
        TemplateGenPath genPath = new TemplateGenPath();
        IEvalScope scope = XLang.newEvalScope();
        genPath.push("a");
        genPath.resolveTop(scope);
        genPath.push("{deltaDir}");
        genPath.resolveTop(scope);
        genPath.push("test");
        genPath.resolveTop(scope);
        String path = genPath.getTargetPath();
        assertEquals("a/test", path);
    }
}
