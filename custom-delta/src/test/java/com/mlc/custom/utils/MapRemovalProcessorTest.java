package com.mlc.custom.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * Map移除, 用于移除Map中包含@prop-ref的项, 并输出移除的项和路径
 */
@Slf4j
public final class MapRemovalProcessorTest {


    private static final class RemovalResult {
        Map<String, Object> filteredMap;
        List<Map<String, Object>> removedItems;
        List<String> removedPaths;

        private RemovalResult(Map<String, Object> filteredMap, List<Map<String, Object>> removedItems, List<String> removedPaths) {
            this.filteredMap = filteredMap;
            this.removedItems = removedItems;
            this.removedPaths = removedPaths;
        }
    }

    public static RemovalResult processMap(Map<String, Object> map) {
        List<Map<String, Object>> removedItems = new ArrayList<>();
        List<String> removedPaths = new ArrayList<>();
        Map<String, Object> filteredMap = filterMap(map, "", removedItems, removedPaths);
        return new RemovalResult(filteredMap, removedItems, removedPaths);
    }

    private static Map<String, Object> filterMap(Map<String, Object> map, String path, List<Map<String, Object>> removedItems, List<String> removedPaths) {
        Map<String, Object> result = new HashMap<>();

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();
            String currentPath = path.isEmpty() ? entry.getKey() : path + "." + entry.getKey();

            if (value instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> valueMap = (Map<String, Object>) value;
                if (containsPropRef(valueMap)) {
                    removedItems.add(valueMap);
                    removedPaths.add(currentPath);
                } else {
                    result.put(entry.getKey(), filterMap(valueMap, currentPath, removedItems, removedPaths));
                }
            } else if (value instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> valueList = (List<Object>) value;
                List<Object> filteredList = filterList(valueList, currentPath, removedItems, removedPaths);
                result.put(entry.getKey(), filteredList);
            } else {
                result.put(entry.getKey(), value);
            }
        }

        return result;
    }

    private static List<Object> filterList(List<Object> list, String path, List<Map<String, Object>> removedItems, List<String> removedPaths) {
        List<Object> result = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            Object item = list.get(i);
            String currentPath = path + "[" + i + "]";

            if (item instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> itemMap = (Map<String, Object>) item;
                if (containsPropRef(itemMap)) {
                    removedItems.add(itemMap);
                    removedPaths.add(currentPath);
                } else {
                    result.add(filterMap(itemMap, currentPath, removedItems, removedPaths));
                }
            } else if (item instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> itemList = (List<Object>) item;
                result.add(filterList(itemList, currentPath, removedItems, removedPaths));
            } else {
                result.add(item);
            }
        }

        return result;
    }

    private static boolean containsPropRef(Map<String, Object> map) {
        return map.keySet().stream()
                  .anyMatch(value -> value instanceof String && value.contains("@prop-ref"));
    }

    public static void main(String... args) {
        // 测试数据
        Map<String, Object> data = new HashMap<>();
        data.put("mName", "AAAAAA");
        List<Object> shebeiToMany = new ArrayList<>();
        Map<String, Object> item1 = new HashMap<>();
        item1.put("number", "111111");
        item1.put("sname", "11111");
        item1.put("chargeUser", Arrays.asList("daslhdhkl1"));
        shebeiToMany.add(item1);

        Map<String, Object> item2 = new HashMap<>();
        item2.put("number", "2222");
        item2.put("sname", "2222");
        List<Map<String, Object>> oneToManyLingJian = new ArrayList<>();
        Map<String, Object> lingJian1 = new HashMap<>();
        lingJian1.put("id", "7ab81");
        lingJian1.put("@prop-ref", "to-one-id");
        oneToManyLingJian.add(lingJian1);
        Map<String, Object> lingJian2 = new HashMap<>();
        lingJian2.put("id", "dasda2");
        lingJian2.put("@prop-ref", "to-one-id");
        oneToManyLingJian.add(lingJian2);
        item2.put("oneToManyLingJian", oneToManyLingJian);
        item2.put("chargeUser", Arrays.asList("d123dakjsd"));
        shebeiToMany.add(item2);

        data.put("shebeiToMany", shebeiToMany);

        // 处理数据
        RemovalResult result = processMap(data);

        // 输出结果
        log.info("Filtered Map: {}", result.filteredMap);

        log.info("Removed Items: ");
        result.removedItems.forEach(System.out::println);

        System.out.println("Removed Paths: ");
        result.removedPaths.forEach(System.out::println);
    }
}


