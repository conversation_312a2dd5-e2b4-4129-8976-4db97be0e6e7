package com.mlc.custom.mapper;

import io.nop.api.core.annotations.data.DataBean;
import java.util.ArrayList;
import java.util.List;

@DataBean
public class ItemData<T> {

    private Long totalNum = 0L;

    private List<T> rows = new ArrayList<>();

//    @GraphQLReturn(bizObjName = "NopAuthUserEx")
    public List<T> getRows() {
        return rows;
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }

    public Long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Long totalNum) {
        this.totalNum = totalNum;
    }
}
