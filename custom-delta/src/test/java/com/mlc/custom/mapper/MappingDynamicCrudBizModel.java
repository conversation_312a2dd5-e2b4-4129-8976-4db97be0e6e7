package com.mlc.custom.mapper;

import io.nop.api.core.annotations.biz.BizModel;
import io.nop.api.core.annotations.biz.BizQuery;
import io.nop.api.core.beans.FieldSelectionBean;
import io.nop.biz.crud.CrudBizModel;
import io.nop.orm.support.DynamicOrmEntity;
import java.util.ArrayList;
import java.util.List;

@BizModel("")
public class MappingDynamicCrudBizModel extends CrudBizModel<DynamicOrmEntity> {

    @BizQuery
//    @GraphQLReturn(bizObjName = BIZ_OBJ_NAME_THIS_OBJ)
    public List<ItemData<DynamicOrmEntity>> atListItem(FieldSelectionBean fieldSelectionBean) {

        List<ItemData<DynamicOrmEntity>> list = new ArrayList<>();
        ItemData<DynamicOrmEntity> itemData = new ItemData<>();
        itemData.setTotalNum(1L);

//        DemoRequest demoRequest = new DemoRequest();
//        demoRequest.setUserId("1");
//        demoRequest.setUserName("demo");
//        itemData.getRows().add(demoRequest);

//        IEntityModel entityModel =
//            ((IOrmEntityDao<?>) daoFor(NopAut.class)).getEntityModel();
//
//        DynamicOrmEntity dynamicOrmEntity = new DynamicOrmEntity(entityModel);
//        dynamicOrmEntity.prop_set("id", "111111");
//        itemData.getRows().add(dynamicOrmEntity);

        list.add(itemData);
        return list;
    }
}
