package com.mlc.custom.mapper;

import static org.junit.jupiter.api.Assertions.assertTrue;

import io.nop.api.core.annotations.autotest.NopTestConfig;
import io.nop.api.core.beans.ApiRequest;
import io.nop.api.core.beans.ApiResponse;
import io.nop.autotest.junit.JunitBaseTestCase;
import io.nop.core.model.selection.FieldSelectionBeanParser;
import io.nop.graphql.core.IGraphQLExecutionContext;
import io.nop.graphql.core.engine.IGraphQLEngine;
import jakarta.inject.Inject;
import java.util.Map;
import org.junit.jupiter.api.Test;


@NopTestConfig(localDb = true, initDatabaseSchema = true)
public class RequestMethodTest extends JunitBaseTestCase {

    @Inject
    protected IGraphQLEngine graphQLEngine;

    @Test
    public void testFindListItem() {
        ApiRequest<Map<String, Object>> requestData = new ApiRequest<>();
        requestData.setSelection(new FieldSelectionBeanParser().parseFromText(null, "totalNum, rows{userName}"));
        IGraphQLExecutionContext context =
            graphQLEngine.newRpcContext(null, "MlcUopUserEx__atListItem", requestData);
        ApiResponse<?> apiResponse = graphQLEngine.executeRpc(context);
        assertTrue(apiResponse.isOk());
    }
}