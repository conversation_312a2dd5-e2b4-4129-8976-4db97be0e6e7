package com.mlc.custom.graphQL;

import io.nop.commons.functional.IAsyncFunctionInvoker;
import io.nop.graphql.core.IGraphQLHook;
import io.nop.graphql.core.ast.GraphQLFieldDefinition;
import io.nop.graphql.core.ast.GraphQLSelectionSet;
import io.nop.graphql.core.engine.DataFetchingEnvironment;
import io.nop.graphql.core.engine.GraphQLExecutor;
import io.nop.graphql.core.engine.IGraphQLEngine;
import io.nop.rpc.api.flowcontrol.IFlowControlRunner;
import java.util.Collection;

public class CustomGraphQLExecutor extends GraphQLExecutor {

    public CustomGraphQLExecutor(IAsyncFunctionInvoker operationInvoker,
        IGraphQLHook graphQLHook, IFlowControlRunner runner, IGraphQLEngine engine) {
        super(operationInvoker, graphQLHook, runner, engine);
    }


    @Override
    protected Object fetchNext(Object value, DataFetchingEnvironment env) {
        GraphQLSelectionSet selectionSet = env.getSelection().getSelectionSet();
        if (selectionSet == null) return value;

        GraphQLFieldDefinition fieldDef = env.getSelection().getFieldDefinition();
        if (fieldDef.getType().isListType()) {
            return fetchList((Collection<?>) value, selectionSet, env);
        } else {
            return fetchSelections(value, selectionSet, env);
        }
    }
}
