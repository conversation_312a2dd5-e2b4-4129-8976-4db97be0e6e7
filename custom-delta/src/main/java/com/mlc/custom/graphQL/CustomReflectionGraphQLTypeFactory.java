package com.mlc.custom.graphQL;

import com.mlc.base.common.beans.group.GroupItemDataBean;
import io.nop.core.type.IGenericType;
import io.nop.graphql.core.ast.GraphQLFieldDefinition;
import io.nop.graphql.core.ast.GraphQLNamedType;
import io.nop.graphql.core.ast.GraphQLObjectDefinition;
import io.nop.graphql.core.ast.GraphQLType;
import io.nop.graphql.core.ast.GraphQLTypeDefinition;
import io.nop.graphql.core.fetcher.BeanPropertyFetcher;
import io.nop.graphql.core.reflection.ReflectionGraphQLTypeFactory;
import io.nop.graphql.core.schema.TypeRegistry;
import io.nop.graphql.core.utils.GraphQLTypeHelper;
import java.util.Map;

public class CustomReflectionGraphQLTypeFactory extends ReflectionGraphQLTypeFactory {

    protected GraphQLType buildViewGroupBeanType(GraphQLType type, TypeRegistry registry,
        Map<String, GraphQLTypeDefinition> creatingTypes, boolean input) {
        String viewGroupTypeName = GroupItemDataBean.OBJ_THIS_NAME + "_" + type;
        GraphQLTypeDefinition objDef = creatingTypes.get(viewGroupTypeName);
        if (objDef == null) {
            objDef = registry.getType(viewGroupTypeName);
            if (objDef == null) {
                objDef = buildViewGroupBeanType(viewGroupTypeName, type, registry, creatingTypes, input);
                registry.registerType(objDef);
            }
        }
        GraphQLNamedType namedType = GraphQLTypeHelper.namedType(viewGroupTypeName);
        namedType.setResolvedType(objDef);
        return namedType;
    }

    protected GraphQLObjectDefinition buildViewGroupBeanType(String typeName, GraphQLType type, TypeRegistry registry,
        Map<String, GraphQLTypeDefinition> creatingTypes, boolean input) {
        GraphQLObjectDefinition objDef = (GraphQLObjectDefinition) buildDef(GroupItemDataBean.OBJ_THIS_NAME, GroupItemDataBean.class,
                                                                            registry, creatingTypes, input);
        objDef = objDef.deepClone();
        creatingTypes.put(typeName, objDef);
        objDef.setName(typeName);
        GraphQLFieldDefinition field = objDef.getField("rows");
        field.setType(GraphQLTypeHelper.listType(type));
        field.setFetcher(BeanPropertyFetcher.INSTANCE);
        return objDef;
    }

    @Override
    protected GraphQLType buildGraphQLType(IGenericType type, String bizObjName, TypeRegistry registry,
        Map<String, GraphQLTypeDefinition> creatingTypes, boolean input) {

        if(type.getRawClass() == GroupItemDataBean.class) {
            IGenericType componentType = type.getTypeParameters().get(0);
            GraphQLType beanType = buildGraphQLType(componentType, bizObjName, registry, creatingTypes, input);
            return buildViewGroupBeanType(beanType, registry, creatingTypes, input);
        }

        return super.buildGraphQLType(type, bizObjName, registry, creatingTypes, input);
    }
}
