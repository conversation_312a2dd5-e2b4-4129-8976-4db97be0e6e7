package com.mlc.custom.replenish.domain.handler;

import com.mlc.base.core.helper.ConvertExtHelper;
import com.mlc.custom.CustomConstants;
import io.nop.api.core.util.SourceLocation;
import io.nop.core.type.IGenericType;
import io.nop.core.type.PredefinedGenericTypes;
import io.nop.xlang.api.XLangCompileTool;
import io.nop.xlang.xdef.IStdDomainOptions;
import io.nop.xlang.xdef.domain.SimpleStdDomainHandler;
import java.util.List;

public class CommonDomainHandlers {

    public static class ListJsonStringType extends SimpleStdDomainHandler {
        @Override
        public boolean isFixedType() {
            return true;
        }

        @Override
        public String getName() {
            return "list-json-string";
        }

        @Override
        public IGenericType getGenericType(boolean mandatory, String options) {
            return PredefinedGenericTypes.STRING_TYPE;
        }

        @Override
        public Object parseProp(String options, SourceLocation loc, String propName, Object text,
            XLangCompileTool cp) {
            return text;
        }
    }


    public static class CsvListWithNullType extends SimpleStdDomainHandler {
        @Override
        public boolean isFixedType() {
            return true;
        }

        @Override
        public String getName() {
            return CustomConstants.CSV_LIST_WITH_NULL;
        }

        @Override
        public IGenericType getGenericType(boolean mandatory, String options) {
            return PredefinedGenericTypes.LIST_STRING_TYPE;
        }

        @Override
        public Object parseProp(String options, SourceLocation loc, String propName, Object text,
            XLangCompileTool cp) {
            List<String> csvListWithNull = ConvertExtHelper.toCsvListWithNull(text);
            return csvListWithNull;
        }
    }

}
