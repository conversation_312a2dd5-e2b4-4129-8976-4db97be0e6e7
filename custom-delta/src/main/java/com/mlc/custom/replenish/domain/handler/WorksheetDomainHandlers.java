package com.mlc.custom.replenish.domain.handler;

import com.mlc.base.core.worksheet.utils.ControlParser;
import io.nop.api.core.util.SourceLocation;
import io.nop.core.type.IGenericType;
import io.nop.xlang.api.XLangCompileTool;
import io.nop.xlang.xdef.domain.SimpleStdDomainHandler;

public class WorksheetDomainHandlers {

    public static class ConvertComponentList extends SimpleStdDomainHandler {
        @Override
        public String getName() {
            return "worksheetComponentList";
        }

        @Override
        public boolean isFixedType() {
            return true;
        }

        @Override
        public IGenericType getGenericType(boolean mandatory, String options) {
            return null;
        }

        @Override
        public Object parseProp(String options, SourceLocation loc, String propName, Object value, XLangCompileTool cp) {
            return new ControlParser().parseControlList((String) value);
        }
    }
}
