package com.mlc.custom.config;

import com.mlc.custom.graphQL.CustomReflectionGraphQLTypeFactory;
import io.nop.graphql.core.reflection.ReflectionGraphQLTypeFactory;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class GraphQLTypeFactoryConfig {

    @PostConstruct
    public void init() {
        try {
            // 直接创建自定义工厂实例并替换
            ReflectionGraphQLTypeFactory.INSTANCE = new CustomReflectionGraphQLTypeFactory();
            log.info("通过Spring配置成功安装自定义GraphQL类型工厂");
        } catch (Exception e) {
            log.error("替换GraphQL类型工厂失败: {}", e.getMessage());
        }
    }
}