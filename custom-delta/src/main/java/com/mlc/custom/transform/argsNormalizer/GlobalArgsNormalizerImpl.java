package com.mlc.custom.transform.argsNormalizer;

import io.nop.graphql.core.IGraphQLExecutionContext;
import io.nop.graphql.core.reflection.IGraphQLArgsNormalizer;
import io.nop.graphql.core.utils.GraphQLArgsHelper;
import java.util.Map;

public class GlobalArgsNormalizerImpl implements IGraphQLArgsNormalizer {

    @Override
    public Map<String, Object> normalize(Map<String, Object> args, IGraphQLExecutionContext context) {
        // 判断是否为动态模型视图
        if(true){
            return Map.of();
        }
        return GraphQLArgsHelper.normalizeQueryArgs(args);
    }
}
