package com.mlc.custom.crud.helper;

import static com.mlc.base.common.MlcBaseCommonConstants.VIEW_INFO;
import static io.nop.graphql.core.GraphQLErrors.ARG_CLASS;
import static io.nop.graphql.core.GraphQLErrors.ARG_METHOD_NAME;
import static io.nop.graphql.core.GraphQLErrors.ARG_RETURN_TYPE;
import static io.nop.graphql.core.GraphQLErrors.ERR_GRAPHQL_ACTION_RETURN_TYPE_MUST_NOT_BE_API_RESPONSE;

import com.mlc.base.common.meta.sheet.ViewInfoMeta;
import com.mlc.application.core.utils.MetaPermissionExtractor;
import com.mlc.application.core.utils.MetaPermissionExtractor.PermissionCategory;
import io.nop.api.core.annotations.biz.BizAction;
import io.nop.api.core.annotations.biz.BizArgsNormalizer;
import io.nop.api.core.annotations.biz.BizLoader;
import io.nop.api.core.annotations.biz.BizMakerChecker;
import io.nop.api.core.annotations.biz.BizMakerCheckerMeta;
import io.nop.api.core.annotations.biz.BizModel;
import io.nop.api.core.annotations.biz.BizMutation;
import io.nop.api.core.annotations.biz.BizQuery;
import io.nop.api.core.annotations.core.Description;
import io.nop.api.core.annotations.directive.Auth;
import io.nop.api.core.auth.ActionAuthMeta;
import io.nop.api.core.beans.ApiResponse;
import io.nop.api.core.convert.ConvertHelper;
import io.nop.api.core.exceptions.NopException;
import io.nop.api.core.util.ApiStringHelper;
import io.nop.api.core.util.MultiCsvSet;
import io.nop.api.core.util.SourceLocation;
import io.nop.commons.collections.KeyedList;
import io.nop.core.context.action.IServiceAction;
import io.nop.core.reflect.IClassModel;
import io.nop.core.reflect.IFunctionModel;
import io.nop.core.reflect.ReflectionManager;
import io.nop.core.reflect.aop.IAopProxy;
import io.nop.core.type.IGenericType;
import io.nop.graphql.core.IBizModelImpl;
import io.nop.graphql.core.IDataFetcher;
import io.nop.graphql.core.ast.GraphQLFieldDefinition;
import io.nop.graphql.core.ast.GraphQLObjectDefinition;
import io.nop.graphql.core.ast.GraphQLOperationType;
import io.nop.graphql.core.fetcher.BeanMethodAction;
import io.nop.graphql.core.fetcher.BeanMethodBatchFetcher;
import io.nop.graphql.core.fetcher.ServiceActionFetcher;
import io.nop.graphql.core.reflection.GraphQLBizModel;
import io.nop.graphql.core.reflection.LazyGraphQLArgsNormalizer;
import io.nop.graphql.core.reflection.ReflectionBizModelBuilder;
import io.nop.graphql.core.reflection.ReflectionGraphQLTypeFactory;
import io.nop.graphql.core.schema.TypeRegistry;
import io.nop.graphql.core.utils.GraphQLNameHelper;
import io.nop.xlang.xmeta.IObjMeta;
import io.nop.xlang.xmeta.impl.ObjPropAuthModel;
import java.util.Collections;

public class ViewReflectionBizModelBuilder extends ReflectionBizModelBuilder {
    public static final ViewReflectionBizModelBuilder INSTANCE = new ViewReflectionBizModelBuilder();


    public GraphQLBizModel build(IObjMeta objMeta, Object bean, TypeRegistry registry) {
        Class<?> clazz = bean.getClass();
        if (IAopProxy.class.isAssignableFrom(clazz))
            clazz = clazz.getSuperclass();

        IClassModel classModel = ReflectionManager.instance().getClassModel(clazz);
        BizModel bizModel = getBizModel(classModel);
        String[] disabledActions = bizModel.disabledActions();
        String[] inheritActions = bizModel.inheritActions();

        String bizObjName = getBizObjName(bizModel, classModel);
        if (bean instanceof IBizModelImpl) {
            String name = ((IBizModelImpl) bean).getBizObjName();
            if (!ApiStringHelper.isEmpty(name))
                bizObjName = name;
        }

        if (ApiStringHelper.isEmpty(bizObjName))
            throw new IllegalArgumentException("nop.err.graphql.empty-bizObjName:" + bean);

        GraphQLBizModel ret = new GraphQLBizModel(bizObjName);

        // note: 从元数据中获取视图权限信息
        KeyedList<ObjPropAuthModel> auths = null;
        if (objMeta.prop_has(VIEW_INFO) && objMeta.prop_get(VIEW_INFO) != null){
            auths = ((ViewInfoMeta)objMeta.prop_get(VIEW_INFO)).getAuths();
        }

        SourceLocation loc = SourceLocation.fromClass(clazz);
        for (IFunctionModel func : classModel.getMethods()) {
            BizMutation mutation = func.getAnnotation(BizMutation.class);
            if (mutation != null) {
                String action = getMutationName(mutation, func);
                if (!isLocalMethod(classModel, func) && !isAllowed(action, disabledActions, inheritActions))
                    continue;
                GraphQLFieldDefinition field = this.buildActionField(bizObjName, bean, GraphQLOperationType.mutation,
                                                                loc, action, func, registry, auths);
                field.setSourceClassModel(classModel);
                field.setOperationName(GraphQLNameHelper.getOperationName(bizObjName, action));
                ret.addMutationAction(action, field);
                continue;
            }

            BizQuery query = func.getAnnotation(BizQuery.class);
            if (query != null) {
                String action = getQueryName(query, func);
                if (!isLocalMethod(classModel, func) && !isAllowed(action, disabledActions, inheritActions))
                    continue;
                GraphQLFieldDefinition field = this.buildActionField(bizObjName, bean, GraphQLOperationType.query,
                                                                loc, action, func, registry, auths);
                field.setSourceClassModel(classModel);
                field.setOperationName(GraphQLNameHelper.getOperationName(bizObjName, action));

                ret.addQueryAction(action, field);
                continue;
            }

            BizAction bizAction = func.getAnnotation(BizAction.class);
            if (bizAction != null) {
                String action = getBizActionName(bizAction, func);
                if (!isLocalMethod(classModel, func) && !isAllowed(action, disabledActions, inheritActions))
                    continue;
                BeanMethodAction gqlAction = buildAction(bean, loc, action, func);
                gqlAction.setSourceClassModel(classModel);
                ret.addBizAction(action, gqlAction);
                continue;
            }

            BizLoader bizLoader = func.getAnnotation(BizLoader.class);
            if (bizLoader != null) {
                String name = getLoaderName(bizLoader, func);
                if (!isLocalMethod(classModel, func) && !isAllowed(name, disabledActions, inheritActions))
                    continue;

                GraphQLFieldDefinition field = buildFetcherField(bizObjName, bean, loc, name, func, registry);
                field.setSourceClassModel(classModel);

                IGenericType returnType = func.getReturnType();
                if (field.getFetcher() instanceof BeanMethodBatchFetcher) {
                    returnType = returnType.getTypeParameters().get(0);
                }
                field.setType(ReflectionGraphQLTypeFactory.INSTANCE.buildGraphQLType(returnType, bizObjName,
                                                                                     getReturnBizObjName(func), registry, false));
                field.setAutoCreate(bizLoader.autoCreateField());

                GraphQLObjectDefinition loaderType = getLoaderForType(bizLoader, registry);
                if (loaderType != null) {
                    loaderType.mergeField(field, false);
                } else {
                    ret.addLoader(name, field);
                }
            }
        }

        return ret;
    }

    protected GraphQLFieldDefinition buildActionField(String bizObjName, Object bean, GraphQLOperationType opType,
                                                    SourceLocation loc, String actionName,
                                                    IFunctionModel func, TypeRegistry registry, KeyedList<ObjPropAuthModel> auths) {
        IServiceAction action = super.buildAction(bean, loc, actionName, func);
        IDataFetcher fetcher = new ServiceActionFetcher(action);

        GraphQLFieldDefinition field = new GraphQLFieldDefinition();
        field.setFunctionModel(func);
        field.setLocation(loc);
        field.setName(GraphQLNameHelper.getOperationName(bizObjName, actionName));
        field.setServiceAction(action);

        ReflectionGraphQLTypeFactory.INSTANCE.getArgDefinitions(field, func, registry);

        Description description = func.getAnnotation(Description.class);
        if (description != null)
            field.setDescription(description.value());

        Auth auth = func.getAnnotation(Auth.class);
        if (auth != null) {
            field.setAuth(new ActionAuthMeta(auth.publicAccess(), ConvertHelper.toCsvSet(auth.roles()), MultiCsvSet.fromText(auth.permissions()),auth.skipWhenNoAuth()));
        } else if (auths != null) {
            MultiCsvSet permissionSets = MetaPermissionExtractor.transformPermission(PermissionCategory.VIEW, actionName, auths);
            field.setAuth(new ActionAuthMeta(false, Collections.emptySet(), permissionSets, false));
        } else {
            String permission = bizObjName + ':' + opType + "|" + bizObjName + ':' + actionName;
            field.setAuth(new ActionAuthMeta(false, Collections.emptySet(), MultiCsvSet.fromText(permission),false));
        }

        BizMakerChecker makerChecker = func.getAnnotation(BizMakerChecker.class);
        if (makerChecker != null) {
            field.setMakerCheckerMeta(new BizMakerCheckerMeta(makerChecker.tryMethod(), makerChecker.cancelMethod()));
        }

        BizArgsNormalizer argsNormalizer = func.getAnnotation(BizArgsNormalizer.class);
        if (argsNormalizer != null && argsNormalizer.value() != null) {
            field.setArgsNormalizer(new LazyGraphQLArgsNormalizer(argsNormalizer.value()));
        }

        field.setFetcher(fetcher);

        if (func.getAsyncReturnType().getRawClass() == ApiResponse.class)
            throw new NopException(ERR_GRAPHQL_ACTION_RETURN_TYPE_MUST_NOT_BE_API_RESPONSE)
                    .param(ARG_METHOD_NAME, func.getName())
                    .param(ARG_CLASS, func.getDeclaringClass().getName())
                    .param(ARG_RETURN_TYPE, func.getReturnType());

        try {
            field.setType(ReflectionGraphQLTypeFactory.INSTANCE.buildGraphQLType(func.getReturnType(), bizObjName,
                    super.getReturnBizObjName(func), registry, false));
        } catch (NopException e) {
            e.addXplStack("buildActionField:" + func.getName());
            throw e;
        }
        return field;
    }
}