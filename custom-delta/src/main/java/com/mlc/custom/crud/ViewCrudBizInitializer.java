package com.mlc.custom.crud;

import com.mlc.custom.crud.helper.ViewReflectionBizModelBuilder;
import io.nop.api.core.convert.ConvertHelper;
import io.nop.biz.BizConstants;
import io.nop.biz.api.IBizObject;
import io.nop.biz.api.IBizObjectManager;
import io.nop.biz.crud.CrudToolProvider;
import io.nop.biz.impl.BizObjectBuildHelper;
import io.nop.commons.functional.IAsyncFunctionInvoker;
import io.nop.dao.api.IDaoProvider;
import io.nop.dao.txn.ITransactionTemplate;
import io.nop.graphql.core.IGraphQLHook;
import io.nop.graphql.core.biz.IBizObjectQueryProcessorBuilder;
import io.nop.graphql.core.biz.IGraphQLBizInitializer;
import io.nop.graphql.core.biz.IGraphQLBizObject;
import io.nop.graphql.core.engine.IGraphQLEngine;
import io.nop.graphql.core.reflection.GraphQLBizModel;
import io.nop.graphql.core.reflection.GraphQLBizModels;
import io.nop.graphql.core.schema.TypeRegistry;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * 视图CRUD业务模型初始化器
 */
@Slf4j
public class ViewCrudBizInitializer implements IGraphQLBizInitializer {

    @Inject
    protected IDaoProvider daoProvider;

    @Inject
    protected ITransactionTemplate transactionTemplate;

    @Inject
    protected IBizObjectManager bizObjectManager;

    @Inject
    protected IGraphQLEngine graphQLEngine;

    @Inject
    protected CrudToolProvider crudToolProvider;

    @Inject
    @Named("nopGraphQLOperationInvoker")
    protected IAsyncFunctionInvoker operationInvoker;

    @Inject
    @Named("nopMetricsGraphQLHook")
    protected IGraphQLHook graphQLHook;

    private ViewCrudBizModel newBizModelBean(IGraphQLBizObject bizObj) {
        ViewCrudBizModel biz = new ViewCrudBizModel();
        biz.setBizObjName(bizObj.getBizObjName());
        biz.setEntityName(bizObj.getEntityName());
        biz.setDaoProvider(daoProvider);
        biz.setTransactionTemplate(transactionTemplate);
        biz.setBizObjectManager(bizObjectManager);
        biz.setGraphQLEngine(graphQLEngine);
        biz.setGraphQLHook(graphQLHook);
        biz.setOperationInvoker(operationInvoker);
        biz.setCrudToolProvider(crudToolProvider);
        return biz;
    }

    @Override
    public void initialize(IGraphQLBizObject bizObj, IBizObjectQueryProcessorBuilder queryProcessorBuilder,
        TypeRegistry typeRegistry, GraphQLBizModels bizModels) {
        log.info("LowCodeCrudBizInitializer initialize");
        Set<String> base = ConvertHelper.toCsvSet(bizObj.getExtAttribute(BizConstants.GRAPHQL_BASE_NAME));
        if (base != null && base.contains("lowCodeCrud")) {

            ViewCrudBizModel bean = newBizModelBean(bizObj);
            GraphQLBizModel bizModel = ViewReflectionBizModelBuilder.INSTANCE.build(
                ((IBizObject) bizObj).getObjMeta(), bean, typeRegistry);
            BizObjectBuildHelper.addDefaultAction(bizObj, bizModel, null);
        }
    }
}
