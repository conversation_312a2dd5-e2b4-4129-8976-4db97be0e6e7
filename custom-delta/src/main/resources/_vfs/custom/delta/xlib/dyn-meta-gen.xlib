<lib x:schema="/nop/schema/xlib.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:xdsl="xdsl" xmlns:xpl="xpl" xmlns:c="c" xmlns:ui="ui">
  <tags>

    <DynMetaPostExtends outputMode="node">
      <attr name="_dsl_root" implicit="true"/>
      <source>
        <thisLib:GenRelateSheetSourceValueField/>
        <thisLib:GenDepartmentRevisionInfo/>
        <thisLib:GenUserRevisionInfo/>
        <thisLib:GenControlCodeRuleAutoExpr/>
      </source>
    </DynMetaPostExtends>

    <GenUserRevisionInfo outputMode="node">
      <attr name="_dsl_root" implicit="true"/>
      <description>
        为低代码用户组件生成默认数据项
      </description>
      <source>
        <meta>
          <props>
            <c:for var="prop" items="${_dsl_root.childByTag('props').children}">
              <c:script><![CDATA[
                  let domainStr = prop.childAttr('schema','domain');
                  if(!domainStr || domainStr != 'user_picker') continue;

                  const propName = prop.getAttr('name');
                ]]></c:script>

              <prop name="${propName}">
                <getter>
                  const users = entity?.${propName};
                  if(_.isEmpty(users)) return null;

                  svcCtx.setDataAuthChecker(null);
                  svcCtx.setActionAuthChecker(null);
                  const detailList = inject('nopBizObjectManager').getBizObject('MlcUopUser')
                                      .invoke('batchGet', {ids: $Jackson.toSet(users)}, null, svcCtx)
                  const result = detailList.map(detail => {
                    if(detail == null) return null;
                      return {
                        'accountId': detail.userId,
                        'fullname': detail.fullname,
                        'status': detail.status,
                      }
                  });
                  return $Jackson.toJsonString(result);
                </getter>
              </prop>
            </c:for>
          </props>
        </meta>
      </source>
    </GenUserRevisionInfo>

    <GenDepartmentRevisionInfo outputMode="node">
      <attr name="_dsl_root" implicit="true"/>
      <description>
        为低代码部门组件生成默认数据项
      </description>
        <source>
          <meta>
            <props>
              <c:for var="prop" items="${_dsl_root.childByTag('props').children}">
                <c:script><![CDATA[
                  let domainStr = prop.childAttr('schema','domain');
                  if(!domainStr || domainStr != 'department') continue;

                  const propName = prop.getAttr('name');
                ]]></c:script>

                <prop name="${propName}">
                <getter>
                  const depts = entity?.${propName};
                  if(_.isEmpty(depts)) return null;

                  svcCtx.setDataAuthChecker(null);
                  svcCtx.setActionAuthChecker(null);
                  const detailList = inject('nopBizObjectManager').getBizObject('MlcUopDepartment')
                                        .invoke('batchGet', {ids: $Jackson.toSet(depts)}, null, svcCtx)
                  const result = detailList.map(detail => {
                    if(detail == null) return null;
                    return {
                        'departmentId': detail.departmentId,
                        'departmentName': detail.departmentName,
                      }
                  });
                  return $Jackson.toJsonString(result);
                </getter>
                </prop>
              </c:for>
            </props>
          </meta>
        </source>
    </GenDepartmentRevisionInfo>

    <GenRelateSheetSourceValueField outputMode="node">
      <attr name="_dsl_root" implicit="true"/>
      <description>
        为低代码工作表关联记录生成默认数据项
      </description>
      <source>
        <meta>
          <props>
            <c:for var="prop" items="${_dsl_root.childByTag('props').children}">
              <c:script><![CDATA[
                const titleName = prop.getAttr('ext:relateSheetTitle');
                const relationName = prop.getAttr('ext:relationName');
                if(!titleName || !relationName) continue;

                // 多对多关联的属性名
                const manyToManyRefPropName = prop.getAttr('ext:manyToManyRefPropName');

                // 关联控件需要显示的字段，卡片和下拉框需要
                const relationShowControls = prop.getAttr('ext:relationShowControls');

            ]]></c:script>

              <prop name="${prop.getAttr('name')}">
                <getter>
                  if(!entity?.${relationName}) return [];

                  const mapToEntityValue = (item) => {
                    const realRowId = <c:choose>
                          <when test="${manyToManyRefPropName}">item.${manyToManyRefPropName}.rowid</when>
                          <otherwise>item.rowid</otherwise>
                        </c:choose>;

                    const realName = <c:choose>
                          <when test="${manyToManyRefPropName}">item.${manyToManyRefPropName}.${titleName}</when>
                          <otherwise>item.${titleName}</otherwise>
                         </c:choose>;
                    if(!realRowId || !realName) return [];

                    const sourcevalue = {
                      rowid: realRowId,
                      name: realName,
                      allowedit: true,
                      allowdelete: true,
                      controlpermissions: "",
                    };

                    <c:for var="showControlName" items="${relationShowControls.$toCsvSet()}" xpl:if="!_.isEmpty(relationShowControls)">
                      <c:if test="${showControlName != titleName}">
                        <c:script>
                          import com.mlc.base.core.MlcBaseCoreConstants;
                          const replacePattern = MlcBaseCoreConstants.RELATION_NAME_REPLACE_PATTERN;
                          const relShowControlName = replacePattern.matcher(showControlName).replaceAll("");
                        </c:script>
                          sourcevalue.${relShowControlName} = <c:choose>
                                <when test="${manyToManyRefPropName}">item.${manyToManyRefPropName}.${relShowControlName}</when>
                                <otherwise>item.${relShowControlName}</otherwise>
                            </c:choose>;
                      </c:if>
                    </c:for>

                    return {
                      sid: realRowId,
                      sourcevalue: $Jackson.toJsonString(sourcevalue), // 前端要求的格式，请勿改动
                    };
                  };

                  <c:if test="${prop.getAttr('ext:kind') == 'to-one'}">
                    return $Jackson.toJsonString([mapToEntityValue(entity.${relationName})]);
                  </c:if>

                  <c:if test="${prop.getAttr('ext:kind') == 'to-many'}">
                    const result = entity.${relationName}.map(mapToEntityValue);
                    return $Jackson.toJsonString(result);
                  </c:if>
                </getter>
              </prop>
            </c:for>
          </props>
        </meta>
      </source>
    </GenRelateSheetSourceValueField>

    <GenControlCodeRuleAutoExpr outputMode="node">
      <attr name="_dsl_root" implicit="true"/>

      <source>
        <meta>
          <props>
            <c:for var="prop" items="${_dsl_root.childByTag('props').children}">
              <c:script><![CDATA[
                const ruleName = prop.getAttr('dyn:codeRule');
                if(!ruleName)
                    continue;

                if(prop.childByTag('autoExpr'))
                    continue;
            ]]></c:script>

              <prop name="${prop.getAttr('name')}">
                <autoExpr when="save">
                    inject('nopBizObjectManager').getBizObject('MlcSystemCodeRule')
                          .invoke('generateCode', {ruleName: "${ruleName}", scope: $scope}, null, svcCtx);
                </autoExpr>
              </prop>
            </c:for>
          </props>
        </meta>
      </source>
    </GenControlCodeRuleAutoExpr>
  </tags>
</lib>