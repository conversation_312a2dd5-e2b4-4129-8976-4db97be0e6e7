<lib x:schema="/nop/schema/xlib.xdef" xmlns:x="/nop/schema/xdsl.xdef" xmlns:xdsl="xdsl" xmlns:xpl="xpl" xmlns:c="c" xmlns:ui="ui">
  <tags>

    <GenControlMeta outputMode="xml">
      <attr name="worksheetControls" />
      <description>
        生成控件元数据相关的扩展配置项
      </description>
      <source>

      </source>
    </GenControlMeta>

    <GetRelateSheetTitle>
      <attr name="propName" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <description>
        获取关联表单的标题控件ID
      </description>
      <source>
        <c:script><![CDATA[
           const findControl =
                worksheetControls.filter(item => item.controlId.contains(propName))[0];
            if(findControl?.dataSource){
              //   const titleControl = inject('nopBizObjectManager').getBizObject("MlcAppWorksheet")
              //                              .invoke('getWorksheetControl', {worksheetId: findControl.dataSource}, svcCtx)
              const titleControl = inject("com.mlc.application.service.entity.MlcAppWorksheetBizModel")
                                          .getWorksheetControl(findControl.dataSource);
              return titleControl?.controlId;
            }
            return null;
        ]]></c:script>
      </source>
    </GetRelateSheetTitle>


    <GetRelateSheetShowType>
      <attr name="propName" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <description>
        获取关联表单的显示类型, 1: 卡片 2: 下拉框  4: 列表 5:标签页
      </description>
      <source>
        <c:script><![CDATA[
           const findControl =
                worksheetControls.filter(item => item.controlId.contains(propName))[0];
            if(findControl?.type == 29){
                return findControl.advancedSetting.showtype;
            }
            return null;
          ]]></c:script>
      </source>
    </GetRelateSheetShowType>

    <GetRelationSearchMaxFetchSize>
      <description>
        获取多态关联表单的最大查询数量
      </description>
      <attr name="propName" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <source>
        <c:script><![CDATA[
           const findControl =
                worksheetControls.filter(item => item.controlId.contains(propName))[0];
            if(findControl?.type == 51){
                return findControl?.advancedSetting?.maxcount;
            }
            return null;
          ]]></c:script>
      </source>
    </GetRelationSearchMaxFetchSize>

    <GetRelationShowControls>
      <description>
        获取关联表单的显示控件,只有在关联表单为卡片或标签页时才有显示控件
      </description>
      <attr name="propName" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <source>
        <c:script><![CDATA[
           const findControl =
                worksheetControls.filter(item => item.controlId.contains(propName))[0];
            if(findControl?.type == 29){
                 const result = findControl?.showControls;
                 if(result.length == 0) return null;
                 return result.join(",");
            }
            return null;
          ]]></c:script>
      </source>
    </GetRelationShowControls>

    <GetAutoIdControlRule>
      <description>
        获取自动编号控件的规则名称
      </description>
      <attr name="propName" mandatory="true"/>
      <attr name="worksheetId" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <source>
        <c:script><![CDATA[
           const findControl =
                worksheetControls.filter(item => item.controlId.contains(propName))[0];
            if(findControl?.type == 33){
                 return worksheetId + "_" + findControl?.controlId;
            }
            return null;
          ]]></c:script>
      </source>
    </GetAutoIdControlRule>


    <GetBoadViewGroupSetting>
      <description>
        获取看板视图分组字段
      </description>
      <attr name="propName" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <source>
        <c:script><![CDATA[
           import java.util.List;
           import java.util.Map;

           const resultMap = new Map();
           resultMap.put('isStatisticsEmpty', false);

           const findControl = worksheetControls.filter(item => item.controlId.contains(propName))[0];

            // 9: 单选框 10: 多选框 11: 下拉框
            if(findControl?.type == 9 || findControl?.type == 10 || findControl?.type == 11){
                const groupList = findControl.options.map2((item, index) => {
                    return {
                      'key': item.key,
                      'name': item.value,
                      'sort': index,
                    };
                 });
                resultMap.put('groupList', groupList);
                resultMap.put('isBeforeGroup', false);
            }else{
                const result = new List();
                result.add(Map.of('key', findControl?.controlId, 'name', findControl?.controlName));

                resultMap.put('groupList', result);
                resultMap.put('isBeforeGroup', true);
            }

            return resultMap;
          ]]></c:script>
      </source>
    </GetBoadViewGroupSetting>

    <GetNavigateGroupSetting>
      <description>
        获取导航视图分组字段
      </description>
      <attr name="navGroupFilter" mandatory="true"/>
      <attr name="worksheetControls" implicit="true"/>
      <source>
        <c:script><![CDATA[
           import java.util.List;
           import java.util.Map;

           const propName = navGroupFilter[0].controlId;
           const findControl = worksheetControls.filter(item => item.controlId.contains(propName))[0];
           logInfo("findControlfindControlfindControlfindControl: {}", findControl.type);

           const resultMap = new Map();
           resultMap.put('controlId', findControl.controlId);
           resultMap.put('isStatisticsEmpty', false);

            // 9: 单选框 10: 多选框 11: 下拉框
            if(findControl?.type == 9 || findControl?.type == 10 || findControl?.type == 11){
                const groupList = findControl.options.map2((item, index) => {
                    return {
                      'key': item.key,
                      'name': item.value,
                      'sort': index,
                    };
                 });
                resultMap.put('groupList', groupList);
                resultMap.put('isBeforeGroup', false);
            }else{
                const result = new List();
                result.add(Map.of('key', findControl?.controlId, 'name', findControl?.controlName));

                resultMap.put('groupList', result);
                resultMap.put('isBeforeGroup', true);
            }

            return resultMap;
          ]]></c:script>
      </source>
    </GetNavigateGroupSetting>
  </tags>

</lib>