<?xml version="1.0" encoding="UTF-8" ?>

<beans x:schema="/nop/schema/beans.xdef" xmlns:x="/nop/schema/xdsl.xdef"
  xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-2.5.xsd">

  <bean id="nopGlobalQueryTransformer" class="com.mlc.custom.transform.query.GlobalQueryTransformerImpl"/>

  <!--  <bean id="nopQueryBeanArgsNormalizer" class="com.mlc.custom.transform.argsNormalizer.GlobalArgsNormalizerImpl"/>-->

  <bean id="customCrudBizInitializer" class="com.mlc.custom.crud.ViewCrudBizInitializer" ioc:sort-order="89">
    <ioc:condition>
      <on-bean>nopDaoProvider</on-bean>
    </ioc:condition>
  </bean>
</beans>