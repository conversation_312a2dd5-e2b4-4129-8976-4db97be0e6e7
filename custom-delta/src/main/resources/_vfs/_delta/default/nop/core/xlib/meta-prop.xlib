<lib x:schema="/nop/schema/xlib.xdef" xmlns:x="/nop/schema/xdsl.xdef" x:extends="super">
  <tags>

    <domain-list-json-string outputMode="node">
      <attr name="propNode"/>
      <source>
        <prop name="${propNode.getAttr('name')}">
          <schema type="String"/>
          <transformIn>
            if (value instanceof String) {
                // 如果是字符串，并且两侧有引号，去掉引号
                return value.replaceAll("^[\"'](.*?)[\"']$", "$1");
            }

            // 默认是 List 处理成 JSON 字符串
            return $Jackson.toJsonString(value);
          </transformIn>
        </prop>
      </source>
    </domain-list-json-string>

    <domain-csv-list-with-null outputMode="node" x:prototype="domain-csv-list">
      <source x:prototype-override="merge">
        <prop name="${propNode.getAttr('name')}">
          <transformOut>
            import com.mlc.base.core.helper.ConvertExtHelper;
            return ConvertExtHelper.toCsvListWithNull(value);
          </transformOut>
        </prop>
      </source>
    </domain-csv-list-with-null>

  </tags>
</lib>