{"meta": {"entityName": "string", "primaryKey": "string", "displayProp": "string", "key": {"name": "string", "props": "string", "displayName": "string"}, "filter": "string", "orderBy": {"name": "string", "desc": "boolean", "nullsFirst": "boolean"}, "tree": {"isLeafProp": "string", "parentProp": "string", "childrenProp": "string", "rootParentValue": "string", "levelProp": "string", "rootLevelValue": "string", "sortProp": "string"}, "selection": {"id": "string", "displayName": "string", "mapping": "string"}, "prop": {"name": "string", "displayName": "string", "type": "string", "mandatory": "boolean", "internal": "boolean", "defaultValue": "any", "mapToProp": "string", "depends": "string", "tagSet": "string", "lazy": "boolean", "insertable": "boolean", "updatable": "boolean", "queryable": "boolean", "sortable": "boolean", "virtual": "boolean", "published": "boolean", "exportable": "boolean", "allowFilterOp": "string", "description": "string", "schema": {"displayName": "string", "precision": "number", "minLength": "number", "pattern": "string", "stdDomain": "string", "scale": "number", "type": "string", "min": "number", "dict": "string", "maxItems": "number", "max": "number", "utf8Length": "number", "orderProp": "string", "domain": "string", "name": "string", "maxLength": "number"}, "graphqlFilter": "string", "graphqlOrderBy": {"name": "string", "desc": "boolean", "nullsFirst": "boolean"}, "graphqlTransFilter": "string", "autoExpr": {"when": "string", "source": "string"}, "transformIn": "expression", "transformOut": "expression", "getter": "expression", "setter": "expression"}}}