package com.mlc.application.core.permission;

import java.util.HashMap;
import java.util.Map;

public interface IResourcePermission {

    Map<String, Object> getDefaultWorksheetPermissions();

    Map<String, Object> getDefaultWorksheetViewPermissions();

    Map<String, Object> getDefaultWorksheetFieldPermissions();

    /**
     * 获取工作表的初始权限, 里面包含了重复的权限 key, 但是 value 不一样
     *
     * @return 初始权限
     */
    Map<String, Object> getInitialWorksheetPermissions();

    default Map<String, Object> getMegerDefaultPermissions() {
        Map<String, Object> permissions = new HashMap<>();
        permissions.putAll(getDefaultWorksheetPermissions());
        permissions.putAll(getDefaultWorksheetViewPermissions());
        permissions.putAll(getDefaultWorksheetFieldPermissions());
        return permissions;
    }
}
