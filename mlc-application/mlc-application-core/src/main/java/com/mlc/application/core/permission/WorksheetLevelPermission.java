package com.mlc.application.core.permission;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工作表级别数据范围权限
 */
@Getter
@AllArgsConstructor
public enum WorksheetLevelPermission implements IPermissionPoint {

    // 数据范围  readLevel:key  =  {roleId1: 20，roleId2: 30，roleId3：100}
    READ_LEVEL("readLevel", 20),
    EDIT_LEVEL("editLevel", 20),
    REMOVE_LEVEL("removeLevel", 20);

    private final String key;
    private final Object defaultValue;

    private static final Map<String, WorksheetLevelPermission> KEY_ONTOLOGY_MAP =
        Arrays.stream(values()).collect(Collectors.toMap(WorksheetLevelPermission::getKey, e -> e));

    public static WorksheetLevelPermission getOntologyByKey(String key) {
        return KEY_ONTOLOGY_MAP.get(key);
    }

    private static final Map<String, Object> KEY_DEFAULT_VALUE_MAP = Arrays.stream(values())
                 .collect(Collectors.toMap(WorksheetLevelPermission::getKey, WorksheetLevelPermission::getDefaultValue));

    public static Map<String, Object> getDefaultPermissions() {
        return Collections.unmodifiableMap(KEY_DEFAULT_VALUE_MAP);
    }
}