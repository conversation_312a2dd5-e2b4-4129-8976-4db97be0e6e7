package com.mlc.application.core.permission;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工作表字段权限
 */
@Getter
@AllArgsConstructor
public enum WorksheetFieldPermission implements IPermissionPoint {

    NOT_ADD("notAdd", true),
    NOT_READ("notRead", true),
    NOT_EDIT("notEdit", true),
//    HIDE_WHEN_ADDED("hideWhenAdded", false),
//    IS_DECRYPT("isDecrypt", false),
    ;

    private final String key;
    private final Object defaultValue;

    // 第一位能否查看，第二位能否编辑（只读），第三位能否添加； 1：能，0：不能
    public static List<WorksheetFieldPermission> getPermissionGroup() {
        return List.of(NOT_READ, NOT_EDIT, NOT_ADD);
    }

    public static Map<String, Object> getDefaultPermissions() {
        return Arrays.stream(values())
                     .collect(Collectors.toMap(WorksheetFieldPermission::getKey, WorksheetFieldPermission::getDefaultValue));
    }
}