package com.mlc.application.core.worksheet.correction;


import com.google.common.base.Strings;
import com.mlc.application.core.worksheet.correction.ControlDataProcess.DifferentialUpdate;
import com.mlc.base.common.enums.meta.DataEditTypeEnum;
import io.nop.orm.OrmConstants;
import io.nop.xlang.xmeta.IObjPropMeta;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SubListProcess {

    public static <R> R parseRows(Object value) {
        return JsonParseUtils.parseToType(value, new com.fasterxml.jackson.core.type.TypeReference<R>() {});
    }

    public static void processDifferentialUpdates(String mainRowId, List<Map<String, Object>> rows,
        IObjPropMeta objPropMeta, DifferentialUpdate differentialUpdate, BiFunction<List<Map<String, Object>>, String, Map<String, Object>> nestedHandler) {

        String itemBizObjName = objPropMeta.getItemBizObjName();
        List<Map<String, Object>> saveList = differentialUpdate.getSaveList(itemBizObjName);
        List<Map<String, Object>> updateList = differentialUpdate.getUpdateList(itemBizObjName);
        List<Map<String, Object>> deleteList = differentialUpdate.getDeleteList(itemBizObjName);

        rows.forEach(row -> {
            Integer editType = (Integer) row.get("editType");
            String tableRowId = row.get("rowid") == null ? null : row.get("rowid").toString();
            if (DataEditTypeEnum.EDIT.getCode() == editType) {

                @SuppressWarnings("unchecked")
                List<Map<String, Object>> bodyList = (List<Map<String, Object>>) row.get("newOldControl");
                // tableRowId 把当前表格循环的行ID传入, 让关联表格内的数据能够关联到当前行
                Map<String, Object> collect = nestedHandler.apply(bodyList, tableRowId);

                if (Strings.isNullOrEmpty(tableRowId)) {
                    // rightProp 为子表外键字段, 将主表的主键设置为它的字段值, 以关联主子表
                    collect.put(objPropMeta.prop_get("ext:joinRightProp").toString(), mainRowId);
                    saveList.add(collect);
                } else {
                    if (!collect.isEmpty()) {
                        collect.put(OrmConstants.PROP_ID, tableRowId);
                        updateList.add(collect);
                    }
                }
            } else {
                deleteList.add(Map.of(OrmConstants.PROP_ID, tableRowId));
            }
        });
    }
}
