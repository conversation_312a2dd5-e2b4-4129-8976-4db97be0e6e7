package com.mlc.application.core.permission;

import com.mlc.base.common.enums.application.AppDataScopeLevelEnum;
import java.util.IdentityHashMap;
import java.util.List;
import java.util.Map;

/**
 * 每个权限的枚举, 用于获取默认权限
 * 也可以通过数据库获取，这里只是简单的获取默认权限
 */
public class ResourcePermissionByEnum implements IResourcePermission {

    @Override
    public Map<String, Object> getDefaultWorksheetPermissions() {
        Map<String, Object> defaultPermissions = WorksheetPermission.getDefaultPermissions();
        defaultPermissions.putAll(WorksheetLevelPermission.getDefaultPermissions());
        return defaultPermissions;
    }

    @Override
    public Map<String, Object> getDefaultWorksheetViewPermissions() {
        return WorksheetViewPermission.getDefaultPermissions();
    }

    @Override
    public Map<String, Object> getDefaultWorksheetFieldPermissions() {
        return WorksheetFieldPermission.getDefaultPermissions();
    }

    @Override
    public Map<String, Object> getInitialWorksheetPermissions() {
        Map<String, Object> identityHashMap = new IdentityHashMap<>(WorksheetPermission.getDefaultPermissions());

        List<Integer> levelValues = AppDataScopeLevelEnum.allValues();
        // 为 defaultPermissions 里面的每个权限添加 levelValues 里面的值
        WorksheetLevelPermission.getDefaultPermissions().forEach((key, value) -> {
            for (Integer newV : levelValues) {
                String newKey = new String(key); // IdentityHashMap 会根据 key 的引用地址来判断是否相等
                identityHashMap.put(newKey, newV);
            }
        });
        return identityHashMap;
    }

}
