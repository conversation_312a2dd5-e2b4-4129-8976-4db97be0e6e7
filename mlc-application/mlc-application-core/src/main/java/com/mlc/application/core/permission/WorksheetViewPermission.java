package com.mlc.application.core.permission;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工作表视图权限
 */
@Getter
@AllArgsConstructor
public enum WorksheetViewPermission implements IPermissionPoint {

    CAN_READ("canRead", false),
    CAN_EDIT("canEdit", false),
    CAN_REMOVE("canRemove", false);

    private final String key;
    private final Object defaultValue;
    

    public static Map<String, Object> getDefaultPermissions() {
        return Arrays.stream(values())
                     .collect(Collectors.toMap(WorksheetViewPermission::getKey, WorksheetViewPermission::getDefaultValue));
    }

}
