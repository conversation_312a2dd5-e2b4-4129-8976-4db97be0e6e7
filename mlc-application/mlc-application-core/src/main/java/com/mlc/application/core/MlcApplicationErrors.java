package com.mlc.application.core;

import static io.nop.api.core.exceptions.ErrorCode.define;

import io.nop.api.core.exceptions.ErrorCode;

public interface MlcApplicationErrors {

    ErrorCode ERR_APPLICATION_QUERY_DATA = define("mlc.err.application.query-data", "数据查询异常");

    ErrorCode ERR_WORKSHEET_CONTROL_NOT_FOUND = define("mlc.err.worksheet.control.not-found", "工作表控件未找到");

    ErrorCode ERR_RELATE_MODEL_MAPPING = define("mlc.err.control-model-mapping", "控件模型对象关系映射异常");
}
