package com.mlc.application.core.worksheet.correction;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.nop.api.core.exceptions.NopException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import static com.mlc.base.common.exception.errors.CommonErrors.ERR_COMMON_BIZ_DATA_FORMAT_CONVERT;

/**
 * JSON 解析工具类
 * 提供统一的 JSON 解析功能，避免重复创建 ObjectMapper 实例
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class JsonParseUtils {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    /**
     * 解析 JSON 字符串为 List<Map<String, Object>>
     *
     * @param jsonString JSON 字符串
     * @return 解析后的列表
     * @throws NopException 解析异常时抛出
     */
    public static List<Map<String, Object>> parseToMapList(Object jsonString) {
        if (jsonString == null) {
            throw new NopException(ERR_COMMON_BIZ_DATA_FORMAT_CONVERT).param("data", "null");
        }
        
        try {
            return OBJECT_MAPPER.readValue(jsonString.toString(), new TypeReference<List<Map<String, Object>>>() {});
        } catch (Exception e) {
            log.error("JSON 解析异常，数据：{}", jsonString, e);
            throw new NopException(ERR_COMMON_BIZ_DATA_FORMAT_CONVERT, e).param("data", jsonString.toString());
        }
    }
    
    /**
     * 解析 JSON 字符串为指定类型
     *
     * @param jsonString JSON 字符串
     * @param typeReference 类型引用
     * @param <T> 目标类型
     * @return 解析后的对象
     * @throws NopException 解析异常时抛出
     */
    public static <T> T parseToType(Object jsonString, TypeReference<T> typeReference) {
        if (jsonString == null) {
            throw new NopException(ERR_COMMON_BIZ_DATA_FORMAT_CONVERT).param("data", "null");
        }
        
        try {
            return OBJECT_MAPPER.readValue(jsonString.toString(), typeReference);
        } catch (Exception e) {
            log.error("JSON 解析异常，数据：{}", jsonString, e);
            throw new NopException(ERR_COMMON_BIZ_DATA_FORMAT_CONVERT, e).param("data", jsonString.toString());
        }
    }
    
    /**
     * 将对象转换为 JSON 字符串
     *
     * @param obj 要转换的对象
     * @return JSON 字符串
     * @throws NopException 转换异常时抛出
     */
    public static String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            log.error("对象转换为JSON异常，对象：{}", obj, e);
            throw new NopException(ERR_COMMON_BIZ_DATA_FORMAT_CONVERT, e).param("object", obj.toString());
        }
    }
}
