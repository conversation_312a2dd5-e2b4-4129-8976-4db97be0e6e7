package com.mlc.application.core.utils;

import io.nop.api.core.util.MultiCsvSet;
import io.nop.commons.collections.KeyedList;
import io.nop.xlang.xmeta.impl.ObjPropAuthModel;

import java.util.Arrays;
import java.util.Locale;
import java.util.Set;

import com.mlc.application.core.permission.WorksheetViewPermission;
import com.mlc.application.core.permission.WorksheetFieldPermission;
import com.mlc.application.core.permission.WorksheetLevelPermission;
import com.mlc.application.core.permission.IPermissionPoint;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 操作权限转换器
 * 负责将GraphQL操作转换为对应的权限集合
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class MetaPermissionExtractor {

    /**
     * 权限类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum PermissionCategory {
        VIEW("view"),
        FIELD("field"),
        LEVEL("level");
        
        private final String category;
    }

    /**
     * 操作前缀分类
     */
    @Getter
    @AllArgsConstructor
    public enum ActionPrefix {
        // 读操作前缀（默认）
        READ(Set.of("get", "find", "query", "list", "search"), WorksheetViewPermission.CAN_READ, WorksheetFieldPermission.NOT_READ, WorksheetLevelPermission.READ_LEVEL),

        // 写操作前缀
        WRITE(Set.of("add", "save", "create", "update"), WorksheetViewPermission.CAN_EDIT, WorksheetFieldPermission.NOT_EDIT, WorksheetLevelPermission.EDIT_LEVEL),

        // 删除操作前缀
        DELETE(Set.of("delete", "remove"), WorksheetViewPermission.CAN_REMOVE, null, WorksheetLevelPermission.REMOVE_LEVEL);

        private final Set<String> prefixes;
        private final WorksheetViewPermission viewPermission;
        private final WorksheetFieldPermission fieldPermission;
        private final WorksheetLevelPermission levelPermission;

        /**
         * 根据操作名称判断属于哪种操作类型
         */
        public static ActionPrefix getActionPrefixByName(String actionName) {
            return Arrays.stream(values())
                    .filter(prefix -> prefix.prefixes.stream().anyMatch(actionName.toLowerCase(Locale.ROOT)::startsWith))
                    .findFirst()
                    .orElse(READ);
        }
        
    }


    /**
     * 根据权限点获取权限
     */
    static MultiCsvSet getPermissionByPoint(IPermissionPoint permissionPoint, KeyedList<ObjPropAuthModel> auths) {
        return auths.stream()
                    .filter(auth -> permissionPoint.getKey().equals(auth.getFor())) // 过滤出指定类型的权限
                    .findFirst()
                    .map(ObjPropAuthModel::getPermissions)
                    .orElse(null);
    }
    
    /**
     * 获取指定权限类别对应的权限
     */
    static MultiCsvSet getPermissionByCategory(PermissionCategory category, String actionName, KeyedList<ObjPropAuthModel> auths) {
        ActionPrefix actionPrefix = ActionPrefix.getActionPrefixByName(actionName);
        
        IPermissionPoint permissionPoint = switch (category) {
            case VIEW -> actionPrefix.getViewPermission();
            case FIELD -> actionPrefix.getFieldPermission();
            case LEVEL -> actionPrefix.getLevelPermission();
        };

        if (permissionPoint == null) {
            log.warn("未找到操作 {} 对应的 {} 类型权限", actionName, category.getCategory());
            return null;
        }
        
        return getPermissionByPoint(permissionPoint, auths);
    }


    /**
     * 获取特定类别的操作权限
     *
     * @param category 权限类别
     * @param actionName 操作名称
     * @param auths 权限模型列表
     * @return 权限集合
     */
    public static MultiCsvSet transformPermission(PermissionCategory category, String actionName, KeyedList<ObjPropAuthModel> auths) {
        if (auths == null || auths.isEmpty()) {
            log.warn("权限模型列表为空，无法转换权限");
            return null;
        }

        if (actionName == null || actionName.isEmpty()) {
            log.warn("操作名称为空，无法转换权限");
            return null;
        }

        return getPermissionByCategory(category, actionName, auths);
    }
}
