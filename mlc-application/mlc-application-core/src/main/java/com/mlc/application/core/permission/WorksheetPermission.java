package com.mlc.application.core.permission;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工作表权限
 * <p>
 * 工作表是特殊的基础的业务对象，工作表权限是由视图/字段权限组成的，所以工作表是没有单独的权限的，
 * 但是因为查询需要，不能每次都去查视图/字段权限，才能确定工作表是否有权限，所以还是要缓存工作表的权限
 * 相关的权限点:"isRead", "isEdit", "isRemove"
 * </p>
 */
@Getter
@AllArgsConstructor
public enum WorksheetPermission implements IPermissionPoint {

    CAN_ADD("canAdd", false),

    // 去掉状态提升权限点，状态提升是由视图权限控制的，改为逻辑判断
    // IS_READ("isRead", false),
    // IS_EDIT("isEdit", false),
    // IS_REMOVE("isRemove", false),

    NAVIGATE_HIDE("navigateHide", false),

    SHARE_VIEW("worksheetShareView", false),
    IMPORT("worksheetImport", false),
    EXPORT("worksheetExport", false),
    DISCUSS("worksheetDiscuss", false),
    LOGGING("worksheetLogging", false),
    BATCH_OPERATION("worksheetBatchOperation", false),
    RECORD_SHARE("recordShare", false),
    RECORD_DISCUSSION("recordDiscussion", false),
    RECORD_SYSTEM_PRINTING("recordSystemPrinting", false),
    RECORD_ATTACHMENT_DOWNLOAD("recordAttachmentDownload", false),
    RECORD_LOGGING("recordLogging", false),
    ;

    private final String key;
    private final Object defaultValue;

    public static Map<String, Object> getDefaultPermissions() {
        return Arrays.stream(values())
                     .collect(Collectors.toMap(WorksheetPermission::getKey, WorksheetPermission::getDefaultValue));
    }
}