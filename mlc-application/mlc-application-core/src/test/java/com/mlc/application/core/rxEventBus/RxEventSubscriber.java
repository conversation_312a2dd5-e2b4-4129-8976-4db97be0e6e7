package com.mlc.application.core.rxEventBus;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RxEventSubscriber {

    /**
     * 订阅的事件类型
     * 一个方法可以订阅多个事件，所以一个事件对应多个方法
     */
    Class<?>[] classes() default {};
}
