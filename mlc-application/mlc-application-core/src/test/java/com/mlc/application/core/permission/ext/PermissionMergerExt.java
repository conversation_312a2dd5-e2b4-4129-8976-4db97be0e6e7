package com.mlc.application.core.permission.ext;

import java.util.HashMap;
import java.util.Map;

public class PermissionMergerExt {

    private static final Map<String, MergeStrategy<?>> mergeStrategies = new HashMap<>();

    static {
        // 初始化时绑定不同权限点和它们的合并策略
        mergeStrategies.put("canRead", new BooleanMergeStrategy());
        mergeStrategies.put("canEdit", new BooleanMergeStrategy());
        mergeStrategies.put("readLevel", new LevelMergeStrategy());
        mergeStrategies.put("editLevel", new LevelMergeStrategy());
    }

    @SuppressWarnings("unchecked")
    public <T> T mergePermission(String permissionKey, T currentValue, T otherValue) {
        MergeStrategy<T> strategy = (MergeStrategy<T>) mergeStrategies.get(permissionKey);
        if (strategy == null) {
            throw new IllegalArgumentException("未找到合适的合并策略:" + permissionKey);
        }
        return strategy.merge(currentValue, otherValue);
    }

}
