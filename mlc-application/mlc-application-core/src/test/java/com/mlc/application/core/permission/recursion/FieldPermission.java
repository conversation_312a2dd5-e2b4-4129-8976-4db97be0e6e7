package com.mlc.application.core.permission.recursion;

import lombok.Data;

@Data
public class FieldPermission implements PermissionNode<FieldPermission> {
    private final String fieldId;
    private boolean notRead;
    private boolean notEdit;
    private boolean notAdd;
    private boolean isDecrypt;

    public FieldPermission(String fieldId, boolean notRead, boolean notEdit, boolean notAdd, boolean isDecrypt) {
        this.fieldId = fieldId;
        this.notRead = notRead;
        this.notEdit = notEdit;
        this.notAdd = notAdd;
        this.isDecrypt = isDecrypt;
    }

    @Override
    public void merge(FieldPermission other) {
        if (other == null) return;
        this.notRead = this.notRead && other.notRead;
        this.notEdit = this.notEdit && other.notEdit;
        this.notAdd = this.notAdd && other.notAdd;
        this.isDecrypt = this.isDecrypt || other.isDecrypt;
    }

    @Override
    public void applyParentRestrictions() {
        // 暂时不需要父级限制，字段权限只受视图控制
    }
}
