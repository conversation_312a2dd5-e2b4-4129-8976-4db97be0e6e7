package com.mlc.application.core.rxEventBus;

import java.lang.reflect.Method;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RxEventSubscriberProcessor implements BeanPostProcessor {

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        // 获取 Bean 的所有方法
        Method[] methods = bean.getClass().getDeclaredMethods();

        // 查找带有 @RxEventSubscriber 注解的方法
        Arrays.stream(methods)
              .filter(method -> method.isAnnotationPresent(RxEventSubscriber.class))
              .forEach(method -> subscribeToEvent(bean, method));

        return bean;
    }

    private void subscribeToEvent(Object bean, Method method) {

        RxEventSubscriber annotation = method.getAnnotation(RxEventSubscriber.class);
        // 遍历 `classes` 属性中的每个事件类型
        for (Class<?> eventType : annotation.classes()) {
            // 订阅事件，将方法作为 lambda 注册到 RxEventBus 的 Observable 中
            RxEventBusEnum.INSTANCE.getObservable()
               .filter(event -> eventType.isAssignableFrom(event.getClass())) // 过滤符合 `classes` 的事件
               .subscribe(event -> {
                   try {
                       method.invoke(bean, event);
                   } catch (Exception e) {
                       log.error("Error invoking method: {}", method.getName(), e);
                   }
               });
        }
    }
}