package com.mlc.application.core.permission.recursion;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import org.junit.jupiter.api.Test;


/**
 * 计算用户最大权限
 * 此版是以字段为最小权限单位的，每个视图权限下面都嵌套着字段权限
 * 但是此项目中视图权限和字段权限是并行的，字段权限也是挂在工作表下面的，所以此版不适用
 */
public class PermissionMerger {

    public static RolePermission mergeRolePermissions(List<RolePermission> rolePermissions) {
        if (rolePermissions == null || rolePermissions.isEmpty()) return null;

        // 以第一个角色权限为基础进行合并
        RolePermission result = rolePermissions.get(0);
        for (int i = 1; i < rolePermissions.size(); i++) {
            result.merge(rolePermissions.get(i));
        }

        // 合并后应用父级权限的限制
        result.applyParentRestrictions();

        return result;
    }

    @Test
    public void exec() throws IOException {
        Random random = new Random();
        List<RolePermission> rolePermissions = new ArrayList<>();
        // 示例数据，分别为角色、工作表、视图和字段
        for (int r = 0; r < 10; r++) {
            RolePermission role = new RolePermission("role" + r);
            for (int w = 0; w < 20; w++) {
                WorksheetPermission sheet = new WorksheetPermission("sheet" + w, random.nextBoolean(), 30, 30, 30);
                for (int v = 0; v < 10; v++) {
                    ViewPermission view = new ViewPermission("view" + v, random.nextBoolean(), random.nextBoolean(), random.nextBoolean());
                    for (int f = 0; f < 50; f++) {
                        FieldPermission field = new FieldPermission("field" + f, random.nextBoolean(), random.nextBoolean(), random.nextBoolean(), random.nextBoolean());
                        view.getFields().put(field.getFieldId(), field);
                    }
                    sheet.getViews().put(view.getViewId(), view);
                }
                role.getWorksheets().put(sheet.getSheetId(), sheet);
            }
            rolePermissions.add(role);
        }

        // 合并多个角色权限
        long l = System.currentTimeMillis();
        RolePermission mergedRole = mergeRolePermissions(rolePermissions);
        System.out.println("Time: " + (System.currentTimeMillis() - l) + "ms");

//        mergedRole.getWorksheets().values().forEach(sheet -> {
//            System.out.println("Sheet: " + sheet.getSheetId());
//            sheet.getViews().values().forEach(view -> {
//                System.out.println("  View: " + view.getViewId());
//                view.getFields().values().forEach(field -> {
//                    System.out.println("    Field: " + field.getFieldId() + ", notRead: " + field.isNotRead() +
//                                           ", notEdit: " + field.isNotEdit() + ", notAdd: " + field.isNotAdd());
//                });
//            });
//        });

        BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter("size.txt"));
        try {
            bufferedWriter.write(new ObjectMapper().writeValueAsString(mergedRole));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        bufferedWriter.close();
    }
}
