package com.mlc.application.core.permission.recursion;

import java.util.HashMap;
import java.util.Map;
import lombok.Data;

@Data
public class WorksheetPermission implements PermissionNode<WorksheetPermission> {
    private final String sheetId;
    private boolean canAdd;
    private int readLevel;
    private int editLevel;
    private int removeLevel;
    private final Map<String, ViewPermission> views = new HashMap<>();

    public WorksheetPermission(String sheetId, boolean canAdd, int readLevel, int editLevel, int removeLevel) {
        this.sheetId = sheetId;
        this.canAdd = canAdd;
        this.readLevel = readLevel;
        this.editLevel = editLevel;
        this.removeLevel = removeLevel;
    }

    @Override
    public void merge(WorksheetPermission other) {
        if (other == null) return;
        this.canAdd = this.canAdd || other.canAdd;
        this.readLevel = Math.max(this.readLevel, other.readLevel);
        this.editLevel = Math.max(this.editLevel, other.editLevel);
        this.removeLevel = Math.max(this.removeLevel, other.removeLevel);

        // 合并视图权限
        for (Map.Entry<String, ViewPermission> entry : other.views.entrySet()) {
            this.views.computeIfAbsent(entry.getKey(), k -> entry.getValue()).merge(entry.getValue());
        }
    }

    @Override
    public void applyParentRestrictions() {
        for (ViewPermission view : views.values()) {
            view.applyParentRestrictions();
        }
    }
}
