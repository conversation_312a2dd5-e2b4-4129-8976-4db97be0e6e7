package com.mlc.application.core.rxEventBus;

import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.subjects.PublishSubject;
import io.reactivex.rxjava3.subjects.Subject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum RxEventBusEnum {

    INSTANCE;

    private final @NonNull Subject<Object> subject;

    RxEventBusEnum() {
        this.subject = PublishSubject.create().toSerialized();
    }

    public @NonNull Observable<Object> getObservable() {
        return subject.hide();
    }

    public @NonNull Observable<Object> toObservable() {
        return subject;
    }

    public void post(Object entity) {
        log.info("Posting event for entity: {}", entity);
        subject.onNext(entity);
    }
}
