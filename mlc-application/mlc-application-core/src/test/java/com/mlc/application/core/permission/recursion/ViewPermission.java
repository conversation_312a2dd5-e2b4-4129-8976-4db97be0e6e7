package com.mlc.application.core.permission.recursion;

import java.util.HashMap;
import java.util.Map;
import lombok.Data;

@Data
public class ViewPermission implements PermissionNode<ViewPermission> {
    private final String viewId;
    private boolean canRead;
    private boolean canEdit;
    private boolean canRemove;
    private final Map<String, FieldPermission> fields = new HashMap<>();

    public ViewPermission(String viewId, boolean canRead, boolean canEdit, boolean canRemove) {
        this.viewId = viewId;
        this.canRead = canRead;
        this.canEdit = canEdit;
        this.canRemove = canRemove;
    }

    @Override
    public void merge(ViewPermission other) {
        if (other == null) return;
        this.canRead = this.canRead || other.canRead;
        this.canEdit = this.canEdit || other.canEdit;
        this.canRemove = this.canRemove || other.canRemove;

        // 合并字段权限
        for (Map.Entry<String, FieldPermission> entry : other.fields.entrySet()) {
            this.fields.computeIfAbsent(entry.getKey(), k -> entry.getValue()).merge(entry.getValue());
        }
    }

    @Override
    public void applyParentRestrictions() {
        // 如果视图不可读，则所有字段也不可读
        if (!canRead) {
            for (FieldPermission field : fields.values()) {
                field.setNotRead(true);
            }
        }
    }
}
