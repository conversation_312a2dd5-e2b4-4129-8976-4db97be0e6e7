package com.mlc.application.core.permission.recursion;

import java.util.HashMap;
import java.util.Map;
import lombok.Data;

@Data
public class RolePermission implements PermissionNode<RolePermission> {
    private String roleId;

    private final Map<String, WorksheetPermission> worksheets = new HashMap<>();

    public RolePermission(String roleId) {
        this.roleId = roleId;
    }

    @Override
    public void merge(RolePermission other) {
        if (other == null) return;

        // 合并工作表权限
        for (Map.Entry<String, WorksheetPermission> entry : other.worksheets.entrySet()) {
            this.worksheets.computeIfAbsent(entry.getKey(), k -> entry.getValue()).merge(entry.getValue());
        }
    }

    @Override
    public void applyParentRestrictions() {
        for (WorksheetPermission sheet : worksheets.values()) {
            sheet.applyParentRestrictions();
        }
    }
}
