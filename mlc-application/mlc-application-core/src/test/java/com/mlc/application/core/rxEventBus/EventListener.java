package com.mlc.application.core.rxEventBus;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class EventListener {

    @RxEventSubscriber(classes = {RxEventBusEnum.class})
    public void onEventReceived(Object event) {
        log.info("Event received: {}", event);
//        Observable.merge(RxEventBusEnum.INSTANCE.getObservable(), RxEventBusEnum.INSTANCE.getObservable())
//          .subscribe(event1 -> {
//              log.info("Received event in A method: {}", event1);
//              // 处理事件的逻辑
//          });
    }
}
