<?xml version="1.0" encoding="UTF-8" ?>
<c:script>
// 根据ORM模型生成dao/entity/xbiz
codeGenerator.withTargetDir("../").renderModel('../../../model/mlc-application.orm.xlsx','/nop/templates/backend', '/',$scope);

codeGenerator.withTargetDir("../mlc-application-dao/src/main/java").renderModel('../../mlc-application-dao/src/main/resources/_vfs/mlc/application/orm/app.orm.xml',
    '/nop/templates/orm-entity','/',$scope);
</c:script>