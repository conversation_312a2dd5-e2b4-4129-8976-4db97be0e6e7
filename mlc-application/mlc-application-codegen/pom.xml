<?xml version="1.0" encoding="UTF-8" ?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>mlc-application</artifactId>
        <groupId>flowweb-zhongzhi</groupId>
        <version>${revision}</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>mlc-application-codegen</artifactId>

    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>io.github.entropy-cloud</groupId>
            <artifactId>nop-ooxml-xlsx</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.entropy-cloud</groupId>
            <artifactId>nop-orm</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.entropy-cloud</groupId>
            <artifactId>nop-graphql-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.entropy-cloud</groupId>
            <artifactId>nop-xlang-debugger</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>