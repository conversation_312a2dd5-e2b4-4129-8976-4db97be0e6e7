package com.mlc.workflow.service.biz.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 节点详情处理器工厂
 * 负责根据节点类型选择合适的处理器
 */
@Component
public class NodeDetailHandlerFactory {

    @Autowired
    private List<NodeDetailHandler> handlers;

    /**
     * 根据节点类型获取对应的处理器
     *
     * @param nodeType 节点类型
     * @return 对应的节点详情处理器
     * @throws IllegalArgumentException 如果没有找到支持该节点类型的处理器
     */
    public NodeDetailHandler getHandler(String nodeType) {
        return Optional.ofNullable(handlers)
                .flatMap(list -> list.stream()
                        .filter(handler -> handler.supports(nodeType))
                        .findFirst())
                .orElseThrow(() -> new IllegalArgumentException("不支持的节点类型: " + nodeType));
    }
}
