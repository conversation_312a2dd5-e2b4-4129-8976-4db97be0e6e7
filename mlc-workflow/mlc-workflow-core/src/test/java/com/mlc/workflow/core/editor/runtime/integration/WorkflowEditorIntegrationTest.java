package com.mlc.workflow.core.editor.runtime.integration;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.*;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.command.GatewayOperations;
import com.mlc.workflow.core.editor.structure.command.NodeOperations;
import com.mlc.workflow.core.editor.structure.WorkflowEditor;
import com.mlc.workflow.core.editor.structure.WorkflowValidator;
import com.mlc.workflow.core.editor.structure.strategy.GatewaySemanticsStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工作流编辑器集成测试
 * 验证设计方案中提到的关键边界场景
 * 
 * <AUTHOR> Team
 */
@SpringJUnitConfig(WorkflowEditorTestConfig.class)
class WorkflowEditorIntegrationTest {
    
    @Autowired
    private WorkflowEditor workflowEditor;
    
    private ProcessNode testProcessNode;
    
    @BeforeEach
    void setUp() {
        // 创建基础流程：开始 -> 审批 -> 结束
        testProcessNode = createBasicWorkflow();
    }
    
    /**
     * 创建基础工作流：开始 -> 审批 -> 结束
     */
    private ProcessNode createBasicWorkflow() {
        ProcessNode processNode = new ProcessNode();
        processNode.setId("test-process");
        processNode.setFlowNodeMap(new HashMap<>());
        
        // 开始事件
        StartEventNode startEvent = new StartEventNode();
        startEvent.setId("start-1");
        startEvent.setName("开始");
        startEvent.setNextId("approval-1");
        
        // 审批节点
        ApprovalNode approvalNode = new ApprovalNode();
        approvalNode.setId("approval-1");
        approvalNode.setName("审批");
        approvalNode.setPrveId("start-1");
        approvalNode.setNextId("99");
        
        // 添加到流程映射
        processNode.getFlowNodeMap().put(startEvent.getId(), startEvent);
        processNode.getFlowNodeMap().put(approvalNode.getId(), approvalNode);
        processNode.setStartEventId(startEvent.getId());
        
        return processNode;
    }
    
    @Test
    void testScenario1_LeftPlacementWithEndOwner() {
        // 场景1：在某普通节点下方 LeftPlacement 新增并行网关，且下游已有一个直连结束（99）的节点
        // 验证 EndOwner 扇入策略
        
        // 在审批节点后添加网关（LeftPlacement）
        GatewayNode gateway = workflowEditor.addGateway(testProcessNode, "approval-1", 
                                                      GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);
        
        assertNotNull(gateway);
        assertEquals(GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL, gateway.getGatewayType());
        
        // 验证网关有两个分支
        assertEquals(2, gateway.getFlowIds().size());
        
        // 验证EndOwner规则：只有一个节点的nextId=99
        long endOwnerCount = testProcessNode.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof IRoutable)
                .map(node -> (IRoutable) node)
                .filter(routable -> "99".equals(routable.getNextId()))
                .count();
        
        assertEquals(1, endOwnerCount, "应该只有一个EndOwner");
        
        // 验证流程结构
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "流程结构应该有效: " + result.getErrors());
    }
    
    @Test
    void testScenario2_DuplicateBranchWithConditionConflict() {
        // 场景2：在唯一分支网关里复制分支，新分支条件与已有分支冲突
        
        // 先添加唯一分支网关
        GatewayNode gateway = workflowEditor.addGateway(testProcessNode, "approval-1", 
                                                      GatewayOperations.PlacementStrategy.NO_MOVE);
        
        // 切换为唯一分支
        workflowEditor.switchGatewayType(testProcessNode, gateway.getId(), 
                                       GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE);
        
        // 复制分支
        String originalBranchId = gateway.getFlowIds().get(0);
        ConditionNode duplicatedBranch = workflowEditor.duplicateBranch(testProcessNode, 
                                                                       gateway.getId(), 
                                                                       originalBranchId, -1);
        
        assertNotNull(duplicatedBranch);
        assertEquals(3, gateway.getFlowIds().size()); // 原来2个 + 复制的1个
        
        // 验证流程结构
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "流程结构应该有效: " + result.getErrors());
    }
    
    @Test
    void testScenario3_DeleteBranchesToTriggerGatewayFlatten() {
        // 场景3：分支逐条删除直至只剩一条，触发"删除网关"扁平化逻辑
        
        // 添加网关
        GatewayNode gateway = workflowEditor.addGateway(testProcessNode, "approval-1", 
                                                      GatewayOperations.PlacementStrategy.NO_MOVE);
        
        // 添加第三个分支
        workflowEditor.addBranch(testProcessNode, gateway.getId(), -1);
        
        assertEquals(3, gateway.getFlowIds().size());
        
        // 删除第一个分支
        String firstBranchId = gateway.getFlowIds().get(0);
        workflowEditor.deleteBranch(testProcessNode, gateway.getId(), firstBranchId);
        
        assertEquals(2, gateway.getFlowIds().size());
        
        // 删除第二个分支，应该触发网关扁平化
        String secondBranchId = gateway.getFlowIds().get(0);
        workflowEditor.deleteBranch(testProcessNode, gateway.getId(), secondBranchId);
        
        // 验证网关已被删除（扁平化）
        assertNull(testProcessNode.getFlowNodeMap().get(gateway.getId()));
        
        // 验证流程结构仍然有效
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "扁平化后流程结构应该有效: " + result.getErrors());
    }
    
    @Test
    void testScenario4_SubProcessEndOwner() {
        // 场景4：在子流程内部新增节点直连结束，不影响主流程EndOwner
        
        // 创建包含子流程的节点
        ApprovalProcessNode subProcessNode = new ApprovalProcessNode();
        subProcessNode.setId("subprocess-1");
        subProcessNode.setName("审批子流程");
        
        // 创建子流程
        ProcessNode subProcess = createBasicWorkflow();
        subProcess.setId("sub-process");
        subProcessNode.setProcessNode(subProcess);
        
        // 插入子流程节点
        NodeOperations.NodeSpec spec = new NodeOperations.NodeSpec("approvalprocess", "审批子流程");
        workflowEditor.insertNode(testProcessNode, "start-1", spec);
        
        // 在子流程中添加节点
        NodeOperations.NodeSpec subSpec = new NodeOperations.NodeSpec("approval", "子流程审批");
        workflowEditor.insertNode(subProcess, "start-1", subSpec);
        
        // 验证主流程和子流程都只有一个EndOwner
        long mainEndOwnerCount = testProcessNode.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof IRoutable)
                .map(node -> (IRoutable) node)
                .filter(routable -> "99".equals(routable.getNextId()))
                .count();
        
        long subEndOwnerCount = subProcess.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof IRoutable)
                .map(node -> (IRoutable) node)
                .filter(routable -> "99".equals(routable.getNextId()))
                .count();
        
        assertEquals(1, mainEndOwnerCount, "主流程应该只有一个EndOwner");
        assertEquals(1, subEndOwnerCount, "子流程应该只有一个EndOwner");
        
        // 验证流程结构
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "包含子流程的流程结构应该有效: " + result.getErrors());
    }
    
    @Test
    void testScenario5_DeleteEndOwnerNode() {
        // 场景5：删除EndOwner节点，自动重选EndOwner并修复所有扇入
        
        // 添加更多节点形成扇入结构
        NodeOperations.NodeSpec spec1 = new NodeOperations.NodeSpec("write", "填写");
        BaseNode writeNode = workflowEditor.insertNode(testProcessNode, "start-1", spec1);
        
        NodeOperations.NodeSpec spec2 = new NodeOperations.NodeSpec("cc", "抄送");
        BaseNode ccNode = workflowEditor.insertNode(testProcessNode, writeNode.getId(), spec2);
        
        // 让抄送节点也指向审批节点（形成扇入）
        if (ccNode instanceof IRoutable routableCc) {
            routableCc.setNextId("approval-1");
        }
        
        // 删除EndOwner节点（approval-1）
        workflowEditor.deleteNode(testProcessNode, "approval-1");
        
        // 验证仍然只有一个EndOwner
        long endOwnerCount = testProcessNode.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof IRoutable)
                .map(node -> (IRoutable) node)
                .filter(routable -> "99".equals(routable.getNextId()))
                .count();
        
        assertEquals(1, endOwnerCount, "删除EndOwner后应该重新选择一个EndOwner");
        
        // 验证流程结构
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "删除EndOwner后流程结构应该有效: " + result.getErrors());
    }
    
    @Test
    void testScenario6_SwitchGatewayTypeWithBranchOperations() {
        // 场景6：改网关类型（并行↔唯一）后，继续对分支进行增删序拷，验证条件与尾部的健壮性
        
        // 添加并行网关
        GatewayNode gateway = workflowEditor.addGateway(testProcessNode, "approval-1", 
                                                      GatewayOperations.PlacementStrategy.NO_MOVE);
        
        // 切换为唯一分支
        workflowEditor.switchGatewayType(testProcessNode, gateway.getId(), 
                                       GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE);
        
        // 添加分支
        workflowEditor.addBranch(testProcessNode, gateway.getId(), -1);
        
        // 调整分支顺序
        List<String> newOrder = new ArrayList<>(gateway.getFlowIds());
        Collections.reverse(newOrder);
        workflowEditor.reorderBranches(testProcessNode, gateway.getId(), newOrder);
        
        // 切换回并行分支
        workflowEditor.switchGatewayType(testProcessNode, gateway.getId(), 
                                       GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL);
        
        // 验证流程结构
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "网关类型切换后流程结构应该有效: " + result.getErrors());
    }
    
    @Test
    void testComplexWorkflowIntegration() {
        // 复杂集成测试：组合多种操作
        
        // 1. 添加网关
        GatewayNode gateway1 = workflowEditor.addGateway(testProcessNode, "approval-1", 
                                                        GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);
        
        // 2. 在分支中添加节点
        String branchId = gateway1.getFlowIds().get(0);
        NodeOperations.NodeSpec spec = new NodeOperations.NodeSpec("write", "填写表单");
        workflowEditor.insertNode(testProcessNode, branchId, spec);
        
        // 3. 添加嵌套网关
        GatewayNode gateway2 = workflowEditor.addGateway(testProcessNode, branchId, 
                                                        GatewayOperations.PlacementStrategy.NO_MOVE);
        
        // 4. 切换网关类型
        workflowEditor.switchGatewayType(testProcessNode, gateway2.getId(), 
                                       GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE);
        
        // 5. 复制分支
        String originalBranch = gateway2.getFlowIds().get(0);
        workflowEditor.duplicateBranch(testProcessNode, gateway2.getId(), originalBranch, -1);
        
        // 验证最终结构
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "复杂工作流结构应该有效: " + result.getErrors());
        
        // 验证EndOwner唯一性
        long endOwnerCount = testProcessNode.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof IRoutable)
                .map(node -> (IRoutable) node)
                .filter(routable -> "99".equals(routable.getNextId()))
                .count();
        
        assertEquals(1, endOwnerCount, "复杂工作流应该只有一个EndOwner");
    }
}
