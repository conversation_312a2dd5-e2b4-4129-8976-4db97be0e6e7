package com.mlc.workflow.core.editor.runtime.integration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.structure.WorkflowEditor;
import com.mlc.workflow.core.editor.runtime.nodes.*;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.WorkflowValidator;
import com.mlc.workflow.core.editor.structure.command.GatewayOperations;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 基于真实工作流数据的测试
 * 使用 workFlow.txt 中的复杂流程进行测试
 */
@Slf4j
@SpringBootTest
@ContextConfiguration(classes = WorkflowEditorTestConfig.class)
public class RealWorkflowTest {

    @Autowired
    private WorkflowEditor workflowEditor;

    @Autowired
    private WorkflowValidator validator;

    private ObjectMapper objectMapper;
    private ProcessNode realWorkflow;

    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        realWorkflow = loadRealWorkflow();
        
        log.info("加载真实工作流，节点数量: {}", realWorkflow.getFlowNodeMap().size());
        log.info("开始事件ID: {}", realWorkflow.getStartEventId());
    }

    /**
     * 从 workFlow.txt 加载真实的工作流数据
     */
    private ProcessNode loadRealWorkflow() throws Exception {
        InputStream inputStream = getClass().getClassLoader()
            .getResourceAsStream("com/mlc/workflow/core/editor/workstr/workFlow.txt");
        
        if (inputStream == null) {
            // 如果在 test/resources 中找不到，尝试从 main/java 中读取
            inputStream = getClass().getClassLoader()
                .getResourceAsStream("../../../../main/java/com/mlc/workflow/core/editor/workstr/workFlow.txt");
        }
        
        assertNotNull(inputStream, "无法找到 workFlow.txt 文件");
        
        JsonNode rootNode = objectMapper.readTree(inputStream);
        
        ProcessNode processNode = new ProcessNode();
        processNode.setId(rootNode.get("id").asText());
        processNode.setStartEventId(rootNode.get("startEventId").asText());
        processNode.setFlowNodeMap(new HashMap<>());
        
        // 解析 flowNodeMap
        JsonNode flowNodeMapNode = rootNode.get("flowNodeMap");
        flowNodeMapNode.fields().forEachRemaining(entry -> {
            JsonNode nodeJson = entry.getValue();
            BaseNode node = parseNode(nodeJson);
            if (node != null) {
                processNode.getFlowNodeMap().put(node.getId(), node);
            }
        });
        
        return processNode;
    }

    /**
     * 解析单个节点
     */
    private BaseNode parseNode(JsonNode nodeJson) {
        String id = nodeJson.get("id").asText();
        int typeId = nodeJson.get("typeId").asInt();
        String name = nodeJson.has("name") ? nodeJson.get("name").asText() : "";
        
        BaseNode node;
        
        // 根据 typeId 创建对应的节点类型
        switch (typeId) {
            case 0: // StartEventNode
                node = new StartEventNode();
                break;
            case 1: // GatewayNode
                node = new GatewayNode();
                if (nodeJson.has("gatewayType")) {
                    ((GatewayNode) node).setGatewayType(nodeJson.get("gatewayType").asInt());
                }
                if (nodeJson.has("flowIds")) {
                    JsonNode flowIdsNode = nodeJson.get("flowIds");
                    java.util.List<String> flowIds = new java.util.ArrayList<>();
                    for (JsonNode flowIdNode : flowIdsNode) {
                        flowIds.add(flowIdNode.asText());
                    }
                    ((GatewayNode) node).setFlowIds(flowIds);
                }
                break;
            case 2: // ConditionNode
                node = new ConditionNode();
                // TODO: 解析 operateCondition
                break;
            case 4: // ApprovalNode
                node = new ApprovalNode();
                break;
            case 5: // CcNode
                node = new CcNode();
                break;
            case 26: // ApprovalProcessNode (子流程)
                node = new ApprovalProcessNode();
                // TODO: 解析嵌套的 processNode
                break;
            case 27: // NotifyNode
                node = new NotifyNode();
                break;
            case 13: // WriteNode (人工节点操作明细)
                node = new WriteNode();
                break;
            case 100: // SystemNode
                node = new SystemNode();
                break;
            default:
                log.warn("未知的节点类型: {}, 跳过节点: {}", typeId, id);
                return null;
        }
        
        // 设置基本属性
        node.setId(id);
        node.setName(name);
        
        if (nodeJson.has("desc")) {
            node.setDesc(nodeJson.get("desc").asText());
        }
        if (nodeJson.has("alias")) {
            node.setAlias(nodeJson.get("alias").asText());
        }
        if (nodeJson.has("prveId")) {
            if (node instanceof IRoutable) {
                ((IRoutable) node).setPrveId(nodeJson.get("prveId").asText());
            }
        }
        if (nodeJson.has("nextId")) {
            if (node instanceof IRoutable) {
                ((IRoutable) node).setNextId(nodeJson.get("nextId").asText());
            }
        }
        
        return node;
    }

    @Test
    void testRealWorkflowStructure() {
        // 验证工作流结构的完整性
        WorkflowValidator.ValidationResult result = validator.validate(realWorkflow);
        
        log.info("验证结果: {}", result.isValid() ? "通过" : "失败");
        if (!result.isValid()) {
            log.error("验证错误: {}", result.getErrors());
        }
        
        // 打印工作流结构信息
        log.info("=== 工作流结构分析 ===");
        log.info("流程ID: {}", realWorkflow.getId());
        log.info("开始事件: {}", realWorkflow.getStartEventId());
        log.info("节点总数: {}", realWorkflow.getFlowNodeMap().size());
        
        // 统计各类型节点数量
        Map<String, Integer> nodeTypeCount = new HashMap<>();
        for (BaseNode node : realWorkflow.getFlowNodeMap().values()) {
            String typeName = node.getClass().getSimpleName();
            nodeTypeCount.put(typeName, nodeTypeCount.getOrDefault(typeName, 0) + 1);
        }
        
        log.info("节点类型统计: {}", nodeTypeCount);
        
        // 基本断言
        assertNotNull(realWorkflow.getId());
        assertNotNull(realWorkflow.getStartEventId());
        assertFalse(realWorkflow.getFlowNodeMap().isEmpty());
        assertTrue(realWorkflow.getFlowNodeMap().containsKey(realWorkflow.getStartEventId()));
    }

    @Test
    void testAddGatewayToRealWorkflow() {
        // 在真实工作流中添加网关
        // 选择一个合适的位置插入网关

        // 找到主流程中的审批流程节点 "65e986ef7cd0e536c5e0367f"
        BaseNode targetNode = realWorkflow.getFlowNodeMap().get("65e986ef7cd0e536c5e0367f");

        log.info("目标节点: {}, 类型: {}",
            targetNode != null ? targetNode.getId() : "null",
            targetNode != null ? targetNode.getClass().getSimpleName() : "null");

        if (targetNode != null && targetNode instanceof IRoutable) {
            log.info("在节点 {} 后添加网关", targetNode.getId());

            try {
                // 添加网关
                GatewayNode gateway = workflowEditor.addGateway(realWorkflow, targetNode.getId(),
                                                                GatewayOperations.PlacementStrategy.NO_MOVE);

                assertNotNull(gateway);
                log.info("成功添加网关: {}", gateway.getId());

                // 验证结果
                WorkflowValidator.ValidationResult result = validator.validate(realWorkflow);
                if (!result.isValid()) {
                    log.error("添加网关后验证失败: {}", result.getErrors());
                }

            } catch (Exception e) {
                log.error("添加网关失败", e);
                fail("添加网关操作失败: " + e.getMessage());
            }
        } else {
            // 如果目标节点不可路由，尝试其他节点
            log.warn("目标节点不可路由，尝试其他节点");

            // 列出所有可路由的节点
            for (Map.Entry<String, BaseNode> entry : realWorkflow.getFlowNodeMap().entrySet()) {
                BaseNode node = entry.getValue();
                if (node instanceof IRoutable) {
                    log.info("可路由节点: {} ({})", node.getId(), node.getClass().getSimpleName());
                }
            }
        }
    }

    @Test
    void testComplexWorkflowOperations() {
        // 在真实工作流上执行复杂操作序列
        log.info("开始复杂工作流操作测试");
        
        // 1. 验证初始状态
        WorkflowValidator.ValidationResult initialResult = validator.validate(realWorkflow);
        log.info("初始验证结果: {}", initialResult.isValid() ? "通过" : "失败");
        
        // 2. 找到合适的操作点
        // 在主流程的审批节点后添加网关
        BaseNode approvalProcess = realWorkflow.getFlowNodeMap().get("65e986ef7cd0e536c5e0367f");
        
        if (approvalProcess != null) {
            try {
                log.info("在审批流程节点 {} 后执行操作", approvalProcess.getId());
                
                // 这里可以添加更多复杂的操作测试
                // 比如：添加网关、添加分支、插入节点等
                
                assertTrue(true, "复杂操作测试完成");
                
            } catch (Exception e) {
                log.error("复杂操作失败", e);
                fail("复杂操作测试失败: " + e.getMessage());
            }
        }
    }
}
