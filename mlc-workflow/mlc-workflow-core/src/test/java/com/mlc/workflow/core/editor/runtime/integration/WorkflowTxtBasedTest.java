package com.mlc.workflow.core.editor.runtime.integration;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;
import com.mlc.workflow.core.editor.runtime.nodes.*;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.WorkflowEditor;
import com.mlc.workflow.core.editor.structure.command.NodeOperations;
import com.mlc.workflow.core.editor.structure.strategy.GatewaySemanticsStrategy;
import com.mlc.workflow.core.editor.structure.WorkflowValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 基于 workFlow.txt 的工作流编辑器测试
 * 测试各种网关、分支、节点操作的正确性
 * 
 * <AUTHOR> Team
 */
@SpringBootTest
@ContextConfiguration(classes = WorkflowEditorTestConfig.class)
public class WorkflowTxtBasedTest {

    @Autowired
    private WorkflowEditor workflowEditor;

    @Autowired
    private WorkflowValidator validator;

    private ProcessNode processNode;

    @BeforeEach
    void setUp() {
        processNode = createWorkflowFromTxt();
    }

    /**
     * 根据 workFlow.txt 创建工作流结构
     */
    private ProcessNode createWorkflowFromTxt() {
        ProcessNode processNode = new ProcessNode();
        processNode.setId("6774e5fcd41c9043e0df39fb");
        processNode.setFlowNodeMap(new HashMap<>());

        // 1. 创建开始事件
        StartEventNode startEvent = new StartEventNode();
        startEvent.setId("65e986df8a2bd83d79a5e557");
        startEvent.setName("工作表事件触发");
        startEvent.setNextId("65e986ef7cd0e536c5e0367f");

        // 2. 创建审批流程节点
        ApprovalProcessNode approvalProcess = new ApprovalProcessNode();
        approvalProcess.setId("65e986ef7cd0e536c5e0367f");
        approvalProcess.setName("未命名审批流程");
        approvalProcess.setPrveId("65e986df8a2bd83d79a5e557");
        approvalProcess.setNextId("65e9874f57c8d64b906944b4");

        // 3. 创建网关节点
        GatewayNode gateway = new GatewayNode();
        gateway.setId("65e9874f57c8d64b906944b4");
        gateway.setName("分支");
        gateway.setPrveId("65e986ef7cd0e536c5e0367f");
        gateway.setNextId("68bc27716c965f0d84818f5d");
        gateway.setGatewayType(2); // 唯一分支网关
        gateway.setFlowIds(new ArrayList<>(Arrays.asList("65e9874f57c8d64b906944b5", "65e9874f57c8d64b906944b6")));

        // 4. 创建条件分支1 (通过)
        ConditionNode condition1 = new ConditionNode();
        condition1.setId("65e9874f57c8d64b906944b5");
        condition1.setPrveId("65e9874f57c8d64b906944b4");
        condition1.setNextId("65e9878c7cd0e536c5e0b268");
        // 设置条件：审批结果 = 通过
        condition1.setOperateCondition(createPassCondition());

        // 5. 创建条件分支2 (其他)
        ConditionNode condition2 = new ConditionNode();
        condition2.setId("65e9874f57c8d64b906944b6");
        condition2.setPrveId("65e9874f57c8d64b906944b4");
        condition2.setNextId("65e987d17cd0e536c5e104d6");
        // 设置条件：其他情况
        condition2.setOperateCondition(createOtherCondition());

        // 6. 创建通知节点1
        NotifyNode notify1 = new NotifyNode();
        notify1.setId("65e9878c7cd0e536c5e0b268");
        notify1.setName("发送站内通知");
        notify1.setPrveId("65e9874f57c8d64b906944b5");
        notify1.setNextId("65e9880246009e42ac67ac76");

        // 7. 创建抄送节点
        CcNode ccNode = new CcNode();
        ccNode.setId("65e9880246009e42ac67ac76");
        ccNode.setName("抄送");
        ccNode.setPrveId("65e9878c7cd0e536c5e0b268");
        ccNode.setNextId("");

        // 8. 创建通知节点2
        NotifyNode notify2 = new NotifyNode();
        notify2.setId("65e987d17cd0e536c5e104d6");
        notify2.setName("发送站内通知");
        notify2.setPrveId("65e9874f57c8d64b906944b6");
        notify2.setNextId("");

        // 9. 创建EndOwner节点
        NotifyNode endOwner = new NotifyNode();
        endOwner.setId("68bc27716c965f0d84818f5d");
        endOwner.setName("通知下级部门");
        endOwner.setPrveId("65e9874f57c8d64b906944b4");
        endOwner.setNextId("99");

        // 添加所有节点到流程映射
        processNode.getFlowNodeMap().put(startEvent.getId(), startEvent);
        processNode.getFlowNodeMap().put(approvalProcess.getId(), approvalProcess);
        processNode.getFlowNodeMap().put(gateway.getId(), gateway);
        processNode.getFlowNodeMap().put(condition1.getId(), condition1);
        processNode.getFlowNodeMap().put(condition2.getId(), condition2);
        processNode.getFlowNodeMap().put(notify1.getId(), notify1);
        processNode.getFlowNodeMap().put(ccNode.getId(), ccNode);
        processNode.getFlowNodeMap().put(notify2.getId(), notify2);
        processNode.getFlowNodeMap().put(endOwner.getId(), endOwner);

        processNode.setStartEventId(startEvent.getId());

        return processNode;
    }

    /**
     * 创建"通过"条件
     */
    private List<List<ConditionGroup>> createPassCondition() {
        ConditionGroup condition = new ConditionGroup();
        condition.setNodeId("65e986ef7cd0e536c5e0367f");
        condition.setFiledId("result");
        condition.setConditionId("9");
        
        List<ConditionGroup> conditionList = new ArrayList<>(Arrays.asList(condition));
        return new ArrayList<>(Arrays.asList(conditionList));
    }

    /**
     * 创建"其他"条件
     */
    private List<List<ConditionGroup>> createOtherCondition() {
        ConditionGroup condition = new ConditionGroup();
        condition.setNodeId("65e986ef7cd0e536c5e0367f");
        condition.setFiledId("result");
        condition.setConditionId("10");
        
        List<ConditionGroup> conditionList = new ArrayList<>(Arrays.asList(condition));
        return new ArrayList<>(Arrays.asList(conditionList));
    }

    @Test
    void testWorkflowStructureValidation() {
        // 验证基础工作流结构
        WorkflowValidator.ValidationResult result = validator.validate(processNode);
        assertTrue(result.isValid(), "基础工作流应该是有效的: " + result.getErrors());
        
        // 验证节点数量
        assertEquals(9, processNode.getFlowNodeMap().size(), "应该有9个节点");
        
        // 验证开始事件
        assertEquals("65e986df8a2bd83d79a5e557", processNode.getStartEventId());
        
        // 验证网关结构
        GatewayNode gateway = (GatewayNode) processNode.getFlowNodeMap().get("65e9874f57c8d64b906944b4");
        assertNotNull(gateway, "网关节点应该存在");
        assertEquals(2, gateway.getFlowIds().size(), "网关应该有2个分支");
        assertEquals(2, gateway.getGatewayType(), "应该是唯一分支网关");
    }

    @Test
    void testAddNewBranchToExistingGateway() {
        // 在现有网关上添加新分支
        String gatewayId = "65e9874f57c8d64b906944b4";
        
        ConditionNode newBranch = workflowEditor.addBranch(processNode, gatewayId, -1);
        
        assertNotNull(newBranch, "新分支应该创建成功");
        
        // 验证网关分支数量增加
        GatewayNode gateway = (GatewayNode) processNode.getFlowNodeMap().get(gatewayId);
        assertEquals(3, gateway.getFlowIds().size(), "网关应该有3个分支");
        assertTrue(gateway.getFlowIds().contains(newBranch.getId()), "新分支应该在网关的分支列表中");
        
        // 验证新分支的连接
        assertEquals(gatewayId, newBranch.getPrveId(), "新分支的前驱应该是网关");
        assertEquals("", newBranch.getNextId(), "新分支应该是空分支");
    }

    @Test
    void testInsertNodeInBranch() {
        // 在分支中插入新节点
        String afterNodeId = "65e9878c7cd0e536c5e0b268"; // 通知节点
        
        NodeOperations.NodeSpec spec = new NodeOperations.NodeSpec("approval", "新增审批");
        BaseNode newNode = workflowEditor.insertNode(processNode, afterNodeId, spec);
        
        assertNotNull(newNode, "新节点应该创建成功");
        assertEquals("新增审批", newNode.getName(), "节点名称应该正确");
        
        // 验证连接关系
        if (newNode instanceof IRoutable routableNew) {
            assertEquals(afterNodeId, routableNew.getPrveId(), "新节点的前驱应该是指定节点");
        }

        BaseNode afterNode = processNode.getFlowNodeMap().get(afterNodeId);
        if (afterNode instanceof IRoutable routableAfter) {
            assertEquals(newNode.getId(), routableAfter.getNextId(), "指定节点的后继应该是新节点");
        }
    }

    @Test
    void testSwitchGatewayType() {
        String gatewayId = "65e9874f57c8d64b906944b4";
        
        // 从唯一分支切换到并行分支
        workflowEditor.switchGatewayType(processNode, gatewayId, GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL);
        
        GatewayNode gateway = (GatewayNode) processNode.getFlowNodeMap().get(gatewayId);
        assertEquals(1, gateway.getGatewayType(), "应该切换为并行网关");
        
        // 验证条件被清除
        ConditionNode condition1 = (ConditionNode) processNode.getFlowNodeMap().get("65e9874f57c8d64b906944b5");
        ConditionNode condition2 = (ConditionNode) processNode.getFlowNodeMap().get("65e9874f57c8d64b906944b6");
        
        assertTrue(condition1.getOperateCondition() == null || condition1.getOperateCondition().isEmpty(), 
                  "并行分支不应该有条件");
        assertTrue(condition2.getOperateCondition() == null || condition2.getOperateCondition().isEmpty(), 
                  "并行分支不应该有条件");
    }

    @Test
    void testDeleteBranchAndTriggerFlattening() {
        String gatewayId = "65e9874f57c8d64b906944b4";
        String branchToDelete = "65e9874f57c8d64b906944b6"; // 删除第二个分支
        
        // 删除一个分支
        workflowEditor.deleteBranch(processNode, gatewayId, branchToDelete);

        GatewayNode gateway = (GatewayNode) processNode.getFlowNodeMap().get(gatewayId);
        assertEquals(1, gateway.getFlowIds().size(), "网关应该只剩1个分支");

        // 删除剩余分支，触发网关扁平化
        String remainingBranch = gateway.getFlowIds().get(0);
        workflowEditor.deleteBranch(processNode, gatewayId, remainingBranch);
        
        // 验证网关被删除
        assertNull(processNode.getFlowNodeMap().get(gatewayId), "网关应该被删除");
        
        // 验证流程连接正确
        WorkflowValidator.ValidationResult result = validator.validate(processNode);
        assertTrue(result.isValid(), "扁平化后的流程应该是有效的: " + result.getErrors());
    }

    @Test
    void testComplexWorkflowOperations() {
        // 复合操作测试：添加分支 -> 插入节点 -> 切换网关类型 -> 删除分支
        String gatewayId = "65e9874f57c8d64b906944b4";
        
        // 1. 添加新分支
        ConditionNode newBranch = workflowEditor.addBranch(processNode, gatewayId, -1);
        
        // 2. 在新分支中插入节点
        NodeOperations.NodeSpec spec = new NodeOperations.NodeSpec("approval", "分支审批");
        BaseNode branchNode = workflowEditor.insertNode(processNode, newBranch.getId(), spec);
        
        // 3. 切换为并行网关
        workflowEditor.switchGatewayType(processNode, gatewayId, GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL);
        
        // 4. 验证最终状态
        GatewayNode gateway = (GatewayNode) processNode.getFlowNodeMap().get(gatewayId);
        assertEquals(3, gateway.getFlowIds().size(), "应该有3个分支");
        assertEquals(1, gateway.getGatewayType(), "应该是并行网关");
        
        // 验证新插入的节点
        assertNotNull(processNode.getFlowNodeMap().get(branchNode.getId()), "新插入的节点应该存在");
        if (branchNode instanceof IRoutable routableBranch) {
            assertEquals(newBranch.getId(), routableBranch.getPrveId(), "节点应该连接到新分支");
        }
        
        // 验证整体流程有效性
        WorkflowValidator.ValidationResult result = validator.validate(processNode);
        assertTrue(result.isValid(), "复合操作后的流程应该是有效的: " + result.getErrors());
    }
}
