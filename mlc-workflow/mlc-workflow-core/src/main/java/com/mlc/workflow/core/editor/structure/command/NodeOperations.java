package com.mlc.workflow.core.editor.structure.command;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.*;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.factory.NodeFactory;
import com.mlc.workflow.core.editor.structure.query.WorkflowQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 普通节点操作命令
 * 实现普通节点的新增、删除、修改等操作
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class NodeOperations {
    
    @Autowired
    private NodeFactory nodeFactory;
    
    @Autowired
    private AutoWireStrategy autoWireStrategy;
    
    @Autowired
    private WorkflowQueryService queryService;
    
    /**
     * 节点规格类
     */
    public static class NodeSpec {
        private String nodeType;
        private String name;
        private Map<String, Object> properties;
        
        public NodeSpec(String nodeType, String name) {
            this.nodeType = nodeType;
            this.name = name;
            this.properties = new HashMap<>();
        }
        
        // Getters and setters
        public String getNodeType() { return nodeType; }
        public void setNodeType(String nodeType) { this.nodeType = nodeType; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Map<String, Object> getProperties() { return properties; }
        public void setProperties(Map<String, Object> properties) { this.properties = properties; }
    }
    
    /**
     * 插入节点
     * @param processNode 流程节点
     * @param afterNodeId 在此节点之后插入
     * @param nodeSpec 新节点规格
     * @return 创建的节点
     */
    public BaseNode insertNode(ProcessNode processNode, String afterNodeId, NodeSpec nodeSpec) {
        if (processNode == null || afterNodeId == null || nodeSpec == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        BaseNode afterNode = processNode.getFlowNodeMap().get(afterNodeId);
        if (afterNode == null) {
            throw new IllegalArgumentException("找不到插入点节点: " + afterNodeId);
        }
        
        if (!(afterNode instanceof IRoutable)) {
            throw new IllegalArgumentException("插入点节点必须是可路由的");
        }
        
        IRoutable routableAfterNode = (IRoutable) afterNode;
        String originalNextId = routableAfterNode.getNextId();
        
        // 创建新节点
        BaseNode newNode = createNodeBySpec(nodeSpec);
        
        // 添加到流程节点映射
        processNode.getFlowNodeMap().put(newNode.getId(), newNode);
        
        // 使用AutoWireStrategy进行连接
        List<BaseNode> prevNodes = Arrays.asList(afterNode);
        
        if ("99".equals(originalNextId)) {
            // 原来指向结束，使用ConnectToEnd
            autoWireStrategy.spliceBetween(processNode, prevNodes, newNode, newNode, null);
            autoWireStrategy.connectToEnd(processNode, newNode);
        } else {
            // 普通插入
            autoWireStrategy.spliceBetween(processNode, prevNodes, newNode, newNode, originalNextId);
            
            // 更新下一个节点的前驱
            if (originalNextId != null && !originalNextId.trim().isEmpty()) {
                BaseNode nextNode = processNode.getFlowNodeMap().get(originalNextId);
                if (nextNode instanceof IRoutable routableNext) {
                    routableNext.setPrveId(newNode.getId());
                }
            }
        }
        
        // 设置新节点的路由信息
        if (newNode instanceof IRoutable routableNew) {
            routableNew.setPrveId(afterNodeId);
        }
        
        log.debug("在节点 {} 后插入新节点 {}", afterNodeId, newNode.getId());
        
        return newNode;
    }
    
    /**
     * 删除节点
     * @param processNode 流程节点
     * @param nodeId 要删除的节点ID
     */
    public void deleteNode(ProcessNode processNode, String nodeId) {
        if (processNode == null || nodeId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        BaseNode nodeToDelete = processNode.getFlowNodeMap().get(nodeId);
        if (nodeToDelete == null) {
            throw new IllegalArgumentException("找不到要删除的节点: " + nodeId);
        }
        
        if (!(nodeToDelete instanceof IRoutable)) {
            throw new IllegalArgumentException("只能删除可路由的节点");
        }
        
        IRoutable routableNode = (IRoutable) nodeToDelete;
        
        // 查找前驱节点
        List<BaseNode> prevNodes = queryService.findPrevNodes(processNode, nodeId);
        String nextId = routableNode.getNextId();
        
        // 连接前驱节点到后继节点
        for (BaseNode prevNode : prevNodes) {
            if (prevNode instanceof IRoutable routablePrev) {
                routablePrev.setNextId(nextId);
            }
        }
        
        // 更新后继节点的前驱
        if (nextId != null && !nextId.trim().isEmpty() && !"99".equals(nextId)) {
            BaseNode nextNode = processNode.getFlowNodeMap().get(nextId);
            if (nextNode instanceof IRoutable routableNext && !prevNodes.isEmpty()) {
                // 如果有多个前驱，选择第一个作为新的前驱
                routableNext.setPrveId(prevNodes.get(0).getId());
            }
        }
        
        // 如果删除的是EndOwner，需要重新选择EndOwner
        if ("99".equals(nextId)) {
            handleEndOwnerDeletion(processNode, prevNodes);
        }
        
        // 从流程节点映射中删除
        processNode.getFlowNodeMap().remove(nodeId);
        
        log.debug("删除节点 {}", nodeId);
    }
    
    /**
     * 更新节点
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @param updates 更新内容
     */
    public void updateNode(ProcessNode processNode, String nodeId, Map<String, Object> updates) {
        if (processNode == null || nodeId == null || updates == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        BaseNode node = processNode.getFlowNodeMap().get(nodeId);
        if (node == null) {
            throw new IllegalArgumentException("找不到节点: " + nodeId);
        }
        
        // 应用更新
        applyUpdatesToNode(node, updates);
        
        // 检查是否需要连接到结束
        Boolean connectToEnd = (Boolean) updates.get("connectToEnd");
        if (Boolean.TRUE.equals(connectToEnd) && node instanceof IRoutable) {
            autoWireStrategy.connectToEnd(processNode, node);
        }
        
        log.debug("更新节点 {}", nodeId);
    }
    
    /**
     * 根据规格创建节点
     */
    private BaseNode createNodeBySpec(NodeSpec spec) {
        String nodeType = spec.getNodeType();
        String name = spec.getName();
        
        return switch (nodeType.toLowerCase()) {
            case "approval" -> nodeFactory.createApprovalNode(name);
            case "write" -> nodeFactory.createWriteNode(name);
            case "cc" -> nodeFactory.createCcNode(name);
            case "notify" -> nodeFactory.createNotifyNode(name);
            case "getmorerecord" -> nodeFactory.createGetMoreRecordNode(name);
            case "approvalprocess" -> nodeFactory.createApprovalProcessNode(name);
            case "subprocess" -> nodeFactory.createSubProcessNode(name);
            default -> throw new IllegalArgumentException("不支持的节点类型: " + nodeType);
        };
    }
    
    /**
     * 处理EndOwner删除
     */
    private void handleEndOwnerDeletion(ProcessNode processNode, List<BaseNode> prevNodes) {
        if (prevNodes.isEmpty()) {
            log.warn("删除EndOwner后没有前驱节点");
            return;
        }
        
        // 选择最后一个前驱作为新的EndOwner
        BaseNode newEndOwner = prevNodes.get(prevNodes.size() - 1);
        if (newEndOwner instanceof IRoutable routableNewEnd) {
            routableNewEnd.setNextId("99");
            
            // 其他前驱指向新的EndOwner
            for (int i = 0; i < prevNodes.size() - 1; i++) {
                BaseNode prevNode = prevNodes.get(i);
                if (prevNode instanceof IRoutable routablePrev) {
                    routablePrev.setNextId(newEndOwner.getId());
                }
            }
            
            log.debug("重新选择EndOwner: {}", newEndOwner.getId());
        }
    }
    
    /**
     * 应用更新到节点
     */
    private void applyUpdatesToNode(BaseNode node, Map<String, Object> updates) {
        for (Map.Entry<String, Object> entry : updates.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            switch (key) {
                case "name" -> {
                    if (value instanceof String) {
                        node.setName((String) value);
                    }
                }
                case "desc" -> {
                    if (value instanceof String) {
                        node.setDesc((String) value);
                    }
                }
                case "alias" -> {
                    if (value instanceof String) {
                        node.setAlias((String) value);
                    }
                }
                // 可以根据需要添加更多属性的更新
                default -> log.debug("忽略未知的更新属性: {}", key);
            }
        }
    }
    
    /**
     * 批量插入节点
     * @param processNode 流程节点
     * @param afterNodeId 在此节点之后插入
     * @param nodeSpecs 节点规格列表
     * @return 创建的节点列表
     */
    public List<BaseNode> insertNodes(ProcessNode processNode, String afterNodeId, List<NodeSpec> nodeSpecs) {
        if (nodeSpecs == null || nodeSpecs.isEmpty()) {
            return Collections.emptyList();
        }
        
        List<BaseNode> createdNodes = new ArrayList<>();
        String currentAfterNodeId = afterNodeId;
        
        for (NodeSpec spec : nodeSpecs) {
            BaseNode newNode = insertNode(processNode, currentAfterNodeId, spec);
            createdNodes.add(newNode);
            currentAfterNodeId = newNode.getId();
        }
        
        return createdNodes;
    }
    
    /**
     * 移动节点到新位置
     * @param processNode 流程节点
     * @param nodeId 要移动的节点ID
     * @param newAfterNodeId 新的前驱节点ID
     */
    public void moveNode(ProcessNode processNode, String nodeId, String newAfterNodeId) {
        if (processNode == null || nodeId == null || newAfterNodeId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        BaseNode nodeToMove = processNode.getFlowNodeMap().get(nodeId);
        if (nodeToMove == null) {
            throw new IllegalArgumentException("找不到要移动的节点: " + nodeId);
        }
        
        // 先断开原有连接
        AutoWireStrategy.DetachContext detachContext = autoWireStrategy.detach(processNode, nodeToMove, nodeToMove);
        
        // 连接到新位置
        BaseNode newAfterNode = processNode.getFlowNodeMap().get(newAfterNodeId);
        if (newAfterNode instanceof IRoutable routableAfter) {
            String originalNext = routableAfter.getNextId();
            autoWireStrategy.spliceBetween(processNode, Arrays.asList(newAfterNode), 
                                         nodeToMove, nodeToMove, originalNext);
        }
        
        log.debug("移动节点 {} 到节点 {} 之后", nodeId, newAfterNodeId);
    }
}
