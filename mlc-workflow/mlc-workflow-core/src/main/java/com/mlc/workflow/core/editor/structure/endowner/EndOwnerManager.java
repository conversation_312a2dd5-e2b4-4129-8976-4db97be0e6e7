package com.mlc.workflow.core.editor.structure.endowner;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.BaseNode;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.query.WorkflowQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * EndOwner 管理器
 * 负责维护流程中 EndOwner 的唯一性和一致性
 * EndOwner 是流程的结束点，nextId="99"，每个流程只能有一个
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class EndOwnerManager {
    
    @Autowired
    private WorkflowQueryService queryService;
    
    /**
     * EndOwner 标识
     */
    public static final String END_OWNER_ID = "99";
    
    /**
     * 查找当前的 EndOwner
     * @param processNode 流程节点
     * @return EndOwner 节点，如果没有则返回 null
     */
    public BaseNode findEndOwner(ProcessNode processNode) {
        if (processNode == null || processNode.getFlowNodeMap() == null) {
            return null;
        }
        
        for (BaseNode node : processNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routableNode) {
                if (END_OWNER_ID.equals(routableNode.getNextId())) {
                    return node;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 验证 EndOwner 的唯一性
     * @param processNode 流程节点
     * @return 验证结果
     */
    public EndOwnerValidationResult validateEndOwnerUniqueness(ProcessNode processNode) {
        EndOwnerValidationResult result = new EndOwnerValidationResult();
        
        if (processNode == null || processNode.getFlowNodeMap() == null) {
            result.addError("流程节点为空");
            return result;
        }
        
        List<BaseNode> endOwners = findAllEndOwners(processNode);
        
        if (endOwners.isEmpty()) {
            result.addError("流程缺少 EndOwner（nextId=99 的节点）");
        } else if (endOwners.size() > 1) {
            result.addError("流程有多个 EndOwner: " + 
                          endOwners.stream().map(BaseNode::getId).toList());
        } else {
            result.setValid(true);
            result.setEndOwner(endOwners.get(0));
        }
        
        return result;
    }
    
    /**
     * 查找所有 EndOwner 节点
     * @param processNode 流程节点
     * @return EndOwner 节点列表
     */
    public List<BaseNode> findAllEndOwners(ProcessNode processNode) {
        List<BaseNode> endOwners = new ArrayList<>();
        
        if (processNode == null || processNode.getFlowNodeMap() == null) {
            return endOwners;
        }
        
        for (BaseNode node : processNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routableNode) {
                if (END_OWNER_ID.equals(routableNode.getNextId())) {
                    endOwners.add(node);
                }
            }
        }
        
        return endOwners;
    }
    
    /**
     * 设置节点为 EndOwner
     * @param processNode 流程节点
     * @param node 要设置为 EndOwner 的节点
     * @return 操作结果
     */
    public EndOwnerOperationResult setAsEndOwner(ProcessNode processNode, BaseNode node) {
        EndOwnerOperationResult result = new EndOwnerOperationResult();
        
        if (!(node instanceof IRoutable routableNode)) {
            result.addError("节点不是可路由的，无法设置为 EndOwner");
            return result;
        }
        
        // 检查当前是否已有 EndOwner
        BaseNode currentEndOwner = findEndOwner(processNode);
        
        if (currentEndOwner != null && !currentEndOwner.getId().equals(node.getId())) {
            // 已有其他 EndOwner，需要处理扇入
            result = handleFanInToEndOwner(processNode, node, currentEndOwner);
        } else {
            // 直接设置为 EndOwner
            routableNode.setNextId(END_OWNER_ID);
            result.setSuccess(true);
            result.setNewEndOwner(node);
            log.debug("设置节点 {} 为 EndOwner", node.getId());
        }
        
        return result;
    }
    
    /**
     * 处理扇入到 EndOwner
     * @param processNode 流程节点
     * @param newNode 新节点
     * @param currentEndOwner 当前 EndOwner
     * @return 操作结果
     */
    private EndOwnerOperationResult handleFanInToEndOwner(ProcessNode processNode, 
                                                         BaseNode newNode, 
                                                         BaseNode currentEndOwner) {
        EndOwnerOperationResult result = new EndOwnerOperationResult();
        
        if (!(newNode instanceof IRoutable routableNew)) {
            result.addError("新节点不是可路由的");
            return result;
        }
        
        // 新节点扇入到现有 EndOwner
        routableNew.setNextId(currentEndOwner.getId());
        result.setSuccess(true);
        result.setNewEndOwner(currentEndOwner);
        result.setFanInNode(newNode);
        
        log.debug("节点 {} 扇入到 EndOwner {}", newNode.getId(), currentEndOwner.getId());
        
        return result;
    }
    
    /**
     * 连接节点到结束
     * @param processNode 流程节点
     * @param node 要连接的节点
     * @return 操作结果
     */
    public EndOwnerOperationResult connectToEnd(ProcessNode processNode, BaseNode node) {
        if (!(node instanceof IRoutable routableNode)) {
            EndOwnerOperationResult result = new EndOwnerOperationResult();
            result.addError("节点不是可路由的，无法连接到结束");
            return result;
        }
        
        BaseNode currentEndOwner = findEndOwner(processNode);
        
        if (currentEndOwner == null) {
            // 没有 EndOwner，设置当前节点为 EndOwner
            return setAsEndOwner(processNode, node);
        } else {
            // 已有 EndOwner，扇入到现有 EndOwner
            return handleFanInToEndOwner(processNode, node, currentEndOwner);
        }
    }
    
    /**
     * 修复 EndOwner：当结构扁平化导致 EndOwner 不再是实际末尾时
     * @param processNode 流程节点
     * @return 修复结果
     */
    public EndOwnerOperationResult repairEndOwnerAfterFlatten(ProcessNode processNode) {
        EndOwnerOperationResult result = new EndOwnerOperationResult();
        
        BaseNode currentEndOwner = findEndOwner(processNode);
        if (currentEndOwner == null) {
            result.addError("没有找到 EndOwner");
            return result;
        }
        
        // 检查是否有其他节点指向 EndOwner
        List<BaseNode> prevNodes = queryService.findPrevNodes(processNode, currentEndOwner.getId());
        
        if (prevNodes.isEmpty()) {
            // EndOwner 没有前驱，可能需要重新选择 EndOwner
            log.warn("EndOwner {} 没有前驱节点，可能需要重新选择", currentEndOwner.getId());
            result.addWarning("EndOwner 没有前驱节点");
            return result;
        }
        
        // 如果有多个前驱指向 EndOwner，选择一个作为新的 EndOwner
        if (prevNodes.size() > 1) {
            BaseNode newEndOwner = selectNewEndOwner(prevNodes);
            if (newEndOwner instanceof IRoutable routableNewEnd) {
                routableNewEnd.setNextId(END_OWNER_ID);
                
                // 其他前驱指向新的 EndOwner
                for (BaseNode prevNode : prevNodes) {
                    if (!prevNode.getId().equals(newEndOwner.getId()) && 
                        prevNode instanceof IRoutable routablePrev) {
                        routablePrev.setNextId(newEndOwner.getId());
                    }
                }
                
                // 原 EndOwner 不再是结束节点
                if (currentEndOwner instanceof IRoutable routableOldEnd) {
                    routableOldEnd.setNextId("");
                }
                
                result.setSuccess(true);
                result.setNewEndOwner(newEndOwner);
                result.setOldEndOwner(currentEndOwner);
                
                log.debug("重新选择 EndOwner: {} -> {}", currentEndOwner.getId(), newEndOwner.getId());
            }
        }
        
        return result;
    }
    
    /**
     * 选择新的 EndOwner
     * @param candidates 候选节点列表
     * @return 选择的新 EndOwner
     */
    private BaseNode selectNewEndOwner(List<BaseNode> candidates) {
        // 简单策略：选择最后一个候选节点
        // 实际应用中可以根据业务需求实现更复杂的选择策略
        return candidates.get(candidates.size() - 1);
    }
    
    /**
     * 删除 EndOwner 时的处理
     * @param processNode 流程节点
     * @param endOwnerToDelete 要删除的 EndOwner
     * @return 操作结果
     */
    public EndOwnerOperationResult handleEndOwnerDeletion(ProcessNode processNode, BaseNode endOwnerToDelete) {
        EndOwnerOperationResult result = new EndOwnerOperationResult();
        
        // 查找前驱节点
        List<BaseNode> prevNodes = queryService.findPrevNodes(processNode, endOwnerToDelete.getId());
        
        if (prevNodes.isEmpty()) {
            result.addWarning("删除的 EndOwner 没有前驱节点");
            return result;
        }
        
        // 选择新的 EndOwner
        BaseNode newEndOwner = selectNewEndOwner(prevNodes);
        if (newEndOwner instanceof IRoutable routableNewEnd) {
            routableNewEnd.setNextId(END_OWNER_ID);
            
            // 其他前驱指向新的 EndOwner
            for (BaseNode prevNode : prevNodes) {
                if (!prevNode.getId().equals(newEndOwner.getId()) && 
                    prevNode instanceof IRoutable routablePrev) {
                    routablePrev.setNextId(newEndOwner.getId());
                }
            }
            
            result.setSuccess(true);
            result.setNewEndOwner(newEndOwner);
            result.setOldEndOwner(endOwnerToDelete);
            
            log.debug("删除 EndOwner {} 后重新选择 EndOwner: {}", 
                     endOwnerToDelete.getId(), newEndOwner.getId());
        }
        
        return result;
    }
    
    /**
     * EndOwner 验证结果
     */
    public static class EndOwnerValidationResult {
        private boolean valid = false;
        private BaseNode endOwner;
        private List<String> errors = new ArrayList<>();
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public BaseNode getEndOwner() { return endOwner; }
        public void setEndOwner(BaseNode endOwner) { this.endOwner = endOwner; }
        public List<String> getErrors() { return errors; }
        public void addError(String error) { this.errors.add(error); }
    }
    
    /**
     * EndOwner 操作结果
     */
    public static class EndOwnerOperationResult {
        private boolean success = false;
        private BaseNode newEndOwner;
        private BaseNode oldEndOwner;
        private BaseNode fanInNode;
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public BaseNode getNewEndOwner() { return newEndOwner; }
        public void setNewEndOwner(BaseNode newEndOwner) { this.newEndOwner = newEndOwner; }
        public BaseNode getOldEndOwner() { return oldEndOwner; }
        public void setOldEndOwner(BaseNode oldEndOwner) { this.oldEndOwner = oldEndOwner; }
        public BaseNode getFanInNode() { return fanInNode; }
        public void setFanInNode(BaseNode fanInNode) { this.fanInNode = fanInNode; }
        public List<String> getErrors() { return errors; }
        public void addError(String error) { this.errors.add(error); }
        public List<String> getWarnings() { return warnings; }
        public void addWarning(String warning) { this.warnings.add(warning); }
    }
}
