必须使用 Unit of Work (UoW) 模式来保证事务和原子性，绝对不能让每个命令随意直接操作 `FlowNodeMap`。**

### 为什么不能随意操作 `FlowNodeMap`？

想象一下一个稍微复杂的操作，比如方案 2 中描述的 **"新增网关（左侧放置）"**，这个操作涉及到：

1.  **创建**一个新的网关节点 `G`。
2.  **创建**两个新的分支叶子节点 `L` 和 `R`。
3.  **修改**原前驱节点 `A` 的 `nextId`，让它指向 `G`。
4.  **修改**网关节点 `G` 的 `flowIds`，指向 `L` 和 `R`。
5.  **修改**网关节点 `G` 的 `nextId`，指向原后继节点 `B`。
6.  **搬迁**一大段节点链（从 `B` 开始的所有下游节点），把它们接到分支 `L` 的后面。这又涉及到修改 `L` 的 `nextId`，以及原链尾节点的 `nextId`。
7.  可能还需要**检查和修复** `EndOwner` 的归属。

这是一个由多个读写步骤组成的**复合操作**。

**如果在执行到第 4 步时，程序因为一个 bug 或非法数据抛出了异常，会发生什么？**

*   `FlowNodeMap` 中已经增加了 `G`, `L`, `R` 三个新节点。
*   前驱节点 `A` 的 `nextId` 已经被改了，它现在指向一个配置不完整的网关 `G`。
*   原始的 `A -> B` 的链接已经断开。
*   **整个流程图(`ProcessNode`)此时处于一个非法的、不一致的、损坏的状态。**

这就是直接操作带来的风险：**缺乏原子性（Atomicity）**。一个逻辑操作要么全部成功，要么完全不发生。

### Unit of Work 如何解决这个问题？

Unit of Work 模式就像一个**“变更暂存区”**或**“事务沙箱”**。它的工作流程如下：

1.  **开始操作，创建 UoW 实例**：
    当一个命令（如 `addGatewayCommand`）开始执行时，它首先会创建一个 `UnitOfWork` 对象。这个 UoW 会持有对原始 `ProcessNode` 的引用。

2.  **在 UoW 中注册变更，而非直接修改**：
    命令在执行过程中，不会去动原始的 `FlowNodeMap`。相反，它会告诉 UoW 它“打算”做什么：
    *   `uow.registerNew(newNode)`：告诉 UoW，我想在提交时增加这个新节点。
    *   `uow.registerDirty(nodeToUpdate)`：告诉 UoW，我想在提交时更新这个节点的属性（比如 `nextId`）。
    *   `uow.registerDeleted(nodeToDelete)`：告诉 UoW，我想在提交时删除这个节点。

    在整个命令的执行期间，原始的 `ProcessNode` 始终是**只读的、保持不变的**。所有的变更意图都被收集在 UoW 内部的队列或集合中。

3.  **操作完成，执行预提交校验**：
    当命令的所有逻辑步骤都执行完毕（即所有变更都已注册到 UoW），就进入了关键的**校验阶段**。
    *   此时，可以调用方案 2 中提到的 **“结构校验器” (`Validator`)**。
    *   这个校验器可以“预演”变更：它看着原始的 `ProcessNode`，并结合 UoW 中暂存的变更，来判断最终结果是否合法（比如，`EndOwner` 是否唯一、有没有悬空节点等）。
    *   **如果校验失败**：操作被中止。因为我们从未修改过原始数据，所以我们只需要简单地**丢弃这个 UoW 对象**即可。系统状态完美地回到了操作开始前的样子，什么都没发生。

4.  **校验通过，执行提交**：
    *   如果校验通过，就调用 `uow.commit()` 方法。
    *   **只有在这个 `commit` 方法内部**，UoW 才会按照顺序，将所有暂存的变更（增、删、改）**一次性地、原子地**应用到真正的 `FlowNodeMap` 上。
    *   提交完成后，`ProcessNode` 从一个有效的旧状态，跃迁到了一个有效的新状态。

### 总结：UoW 在方案 2 中的核心作用

*   **原子性 (Atomicity)**：保证一个业务操作（如“新增网关”）的所有底层数据修改要么全部发生，要么全部不发生，杜绝了中间的“半成品”状态。
*   **一致性 (Consistency)**：通过“预提交校验”机制，确保了只有在变更结果合法的情况下，才会真正修改数据。这保证了流程图始终处于一个有效的、遵循业务规则的状态。
*   **关注点分离 (Separation of Concerns)**：
    *   **命令 (Commands)**：负责业务逻辑的编排（应该创建什么、连接什么）。
    *   **UoW**：负责跟踪变更、管理事务生命周期。
    *   **校验器 (Validators)**：负责业务规则的检查。
    *   **仓库 (Repository)**：负责最终的持久化（`commit` 的最后一步可能是调用 `repository.save(processNode)`）。