package com.mlc.workflow.core.editor.structure;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.*;
import com.mlc.workflow.core.editor.structure.command.BranchOperations;
import com.mlc.workflow.core.editor.structure.command.GatewayOperations;
import com.mlc.workflow.core.editor.structure.command.NodeOperations;
import com.mlc.workflow.core.editor.structure.transaction.UnitOfWork;
import com.mlc.workflow.core.editor.structure.query.WorkflowQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 工作流编辑器
 * 对外服务接口，整合所有操作命令和校验器，提供统一的编辑入口
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
public class WorkflowEditor {
    
    @Autowired
    private GatewayOperations gatewayOperations;
    
    @Autowired
    private BranchOperations branchOperations;
    
    @Autowired
    private NodeOperations nodeOperations;
    
    @Autowired
    private UnitOfWork unitOfWork;
    
    @Autowired
    private WorkflowValidator validator;
    
    @Autowired
    private WorkflowQueryService queryService;
    
    // ==================== 网关操作 ====================
    
    /**
     * 新增网关
     * @param processNode 流程节点
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @return 创建的网关节点
     */
    public GatewayNode addGateway(ProcessNode processNode, String atNodeId, 
                                GatewayOperations.PlacementStrategy placement) {
        unitOfWork.begin(processNode);
        try {
            GatewayNode gateway = gatewayOperations.addGateway(processNode, atNodeId, placement);
            unitOfWork.recordCreate(gateway);
            
            if (unitOfWork.commit()) {
                log.info("成功新增网关: {}", gateway.getId());
                return gateway;
            } else {
                throw new RuntimeException("新增网关失败");
            }
        } catch (Exception e) {
            try {
                unitOfWork.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }
    
    /**
     * 删除网关
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     */
    public void deleteGateway(ProcessNode processNode, String gatewayId) {
        unitOfWork.begin(processNode);
        try {
            GatewayNode gateway = queryService.findGateway(processNode, gatewayId);
            if (gateway != null) {
                unitOfWork.recordDelete(gateway);
            }
            
            gatewayOperations.deleteGateway(processNode, gatewayId);
            
            if (unitOfWork.commit()) {
                log.info("成功删除网关: {}", gatewayId);
            } else {
                throw new RuntimeException("删除网关失败");
            }
        } catch (Exception e) {
            try {
                unitOfWork.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 切换网关类型
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param toType 目标类型
     */
    public void switchGatewayType(ProcessNode processNode, String gatewayId, Integer toType) {
        unitOfWork.begin(processNode);
        try {
            GatewayNode gateway = queryService.findGateway(processNode, gatewayId);
            if (gateway != null) {
                GatewayNode oldGateway = cloneGateway(gateway);
                gatewayOperations.switchGatewayType(processNode, gatewayId, toType);
                unitOfWork.recordUpdate(gatewayId, oldGateway, gateway);
            }
            
            if (unitOfWork.commit()) {
                log.info("成功切换网关类型: {} -> {}", gatewayId, toType);
            } else {
                throw new RuntimeException("切换网关类型失败");
            }
        } catch (Exception e) {
            try {
                unitOfWork.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    // ==================== 分支操作 ====================
    
    /**
     * 新增分支
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param position 插入位置
     * @return 创建的分支叶子节点
     */
    public ConditionNode addBranch(ProcessNode processNode, String gatewayId, int position) {
        unitOfWork.begin(processNode);
        try {
            ConditionNode branch = branchOperations.addBranch(processNode, gatewayId, position);
            unitOfWork.recordCreate(branch);
            
            if (unitOfWork.commit()) {
                log.info("成功新增分支: {}", branch.getId());
                return branch;
            } else {
                throw new RuntimeException("新增分支失败");
            }
        } catch (Exception e) {
            try {
                unitOfWork.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 删除分支
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param branchLeafId 分支叶子ID
     */
    public void deleteBranch(ProcessNode processNode, String gatewayId, String branchLeafId) {
        unitOfWork.begin(processNode);
        try {
            // 记录要删除的分支链
            List<BaseNode> branchChain = queryService.findBranchChain(processNode, branchLeafId);
            for (BaseNode node : branchChain) {
                unitOfWork.recordDelete(node);
            }
            
            branchOperations.deleteBranch(processNode, gatewayId, branchLeafId);
            
            if (unitOfWork.commit()) {
                log.info("成功删除分支: {}", branchLeafId);
            } else {
                throw new RuntimeException("删除分支失败");
            }
        } catch (Exception e) {
            try {
                unitOfWork.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }
    
    /**
     * 调整分支顺序
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param newOrder 新的分支顺序
     */
    public void reorderBranches(ProcessNode processNode, String gatewayId, List<String> newOrder) {
        unitOfWork.begin(processNode);
        try {
            GatewayNode gateway = queryService.findGateway(processNode, gatewayId);
            if (gateway != null) {
                GatewayNode oldGateway = cloneGateway(gateway);
                branchOperations.reorderBranches(processNode, gatewayId, newOrder);
                unitOfWork.recordUpdate(gatewayId, oldGateway, gateway);
            }
            
            if (unitOfWork.commit()) {
                log.info("成功调整分支顺序: {}", gatewayId);
            } else {
                throw new RuntimeException("调整分支顺序失败");
            }
        } catch (Exception e) {
            try {
                unitOfWork.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 复制分支
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param branchLeafId 要复制的分支叶子ID
     * @param position 插入位置
     * @return 复制的分支叶子节点
     */
    public ConditionNode duplicateBranch(ProcessNode processNode, String gatewayId, 
                                       String branchLeafId, int position) {
        unitOfWork.begin(processNode);
        try {
            ConditionNode newBranch = branchOperations.duplicateBranch(processNode, gatewayId, branchLeafId, position);
            
            // 记录复制的分支链
            List<BaseNode> newBranchChain = queryService.findBranchChain(processNode, newBranch.getId());
            for (BaseNode node : newBranchChain) {
                unitOfWork.recordCreate(node);
            }
            
            if (unitOfWork.commit()) {
                log.info("成功复制分支: {} -> {}", branchLeafId, newBranch.getId());
                return newBranch;
            } else {
                throw new RuntimeException("复制分支失败");
            }
        } catch (Exception e) {
            try {
                unitOfWork.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    // ==================== 普通节点操作 ====================
    
    /**
     * 插入节点
     * @param processNode 流程节点
     * @param afterNodeId 在此节点之后插入
     * @param nodeSpec 新节点规格
     * @return 创建的节点
     */
    public BaseNode insertNode(ProcessNode processNode, String afterNodeId, NodeOperations.NodeSpec nodeSpec) {
        unitOfWork.begin(processNode);
        try {
            BaseNode newNode = nodeOperations.insertNode(processNode, afterNodeId, nodeSpec);
            unitOfWork.recordCreate(newNode);
            
            if (unitOfWork.commit()) {
                log.info("成功插入节点: {}", newNode.getId());
                return newNode;
            } else {
                throw new RuntimeException("插入节点失败");
            }
        } catch (Exception e) {
            try {
                unitOfWork.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 删除节点
     * @param processNode 流程节点
     * @param nodeId 要删除的节点ID
     */
    public void deleteNode(ProcessNode processNode, String nodeId) {
        unitOfWork.begin(processNode);
        try {
            BaseNode node = processNode.getFlowNodeMap().get(nodeId);
            if (node != null) {
                unitOfWork.recordDelete(node);
            }
            
            nodeOperations.deleteNode(processNode, nodeId);
            
            if (unitOfWork.commit()) {
                log.info("成功删除节点: {}", nodeId);
            } else {
                throw new RuntimeException("删除节点失败");
            }
        } catch (Exception e) {
            try {
                unitOfWork.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 更新节点
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @param updates 更新内容
     */
    public void updateNode(ProcessNode processNode, String nodeId, Map<String, Object> updates) {
        unitOfWork.begin(processNode);
        try {
            BaseNode node = processNode.getFlowNodeMap().get(nodeId);
            if (node != null) {
                BaseNode oldNode = cloneNode(node);
                nodeOperations.updateNode(processNode, nodeId, updates);
                unitOfWork.recordUpdate(nodeId, oldNode, node);
            }
            
            if (unitOfWork.commit()) {
                log.info("成功更新节点: {}", nodeId);
            } else {
                throw new RuntimeException("更新节点失败");
            }
        } catch (Exception e) {
            try {
                unitOfWork.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    // ==================== 验证操作 ====================
    
    /**
     * 验证流程
     * @param processNode 流程节点
     * @return 验证结果
     */
    public WorkflowValidator.ValidationResult validate(ProcessNode processNode) {
        return validator.validate(processNode);
    }
    
    // ==================== 查询操作 ====================
    
    /**
     * 查找前驱节点
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @return 前驱节点列表
     */
    public List<BaseNode> findPrevNodes(ProcessNode processNode, String nodeId) {
        return queryService.findPrevNodes(processNode, nodeId);
    }
    
    /**
     * 查找网关
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @return 网关节点
     */
    public GatewayNode findGateway(ProcessNode processNode, String nodeId) {
        return queryService.findGateway(processNode, nodeId);
    }
    
    /**
     * 查找分支链
     * @param processNode 流程节点
     * @param branchLeafId 分支叶子ID
     * @return 分支链中的所有节点
     */
    public List<BaseNode> findBranchChain(ProcessNode processNode, String branchLeafId) {
        return queryService.findBranchChain(processNode, branchLeafId);
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 克隆网关节点（简单实现）
     */
    private GatewayNode cloneGateway(GatewayNode gateway) {
        // 这里应该实现深拷贝
        // 为了简化，返回原节点（实际应用中需要完整的克隆实现）
        return gateway;
    }
    
    /**
     * 克隆节点（简单实现）
     */
    private BaseNode cloneNode(BaseNode node) {
        // 这里应该实现深拷贝
        // 为了简化，返回原节点（实际应用中需要完整的克隆实现）
        return node;
    }
}
