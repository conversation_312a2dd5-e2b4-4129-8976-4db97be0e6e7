package com.mlc.workflow.core.editor.structure.factory;

import com.mlc.workflow.core.editor.runtime.nodes.*;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.UUID;

/**
 * 节点工厂
 * 统一创建各种类型的节点，保证默认值与ID生成的一致性
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class NodeFactory {
    
    /**
     * 生成唯一的节点ID
     * @return 节点ID
     */
    public String generateNodeId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 创建开始事件节点
     * @param name 节点名称
     * @return 开始事件节点
     */
    public StartEventNode createStartEventNode(String name) {
        StartEventNode node = new StartEventNode();
        node.setId(generateNodeId());
        node.setName(name != null ? name : "开始事件");
        node.setTypeId(0);
        node.setDesc("");
        node.setAlias("");
        node.setNextId("");
        node.setSelectNodeId("");
        node.setSelectNodeName("");
        node.setIsException(false);
        node.setAccounts(new ArrayList<>());
        
        log.debug("创建开始事件节点: {}", node.getId());
        return node;
    }
    
    /**
     * 创建网关节点（默认并行）
     * @param name 节点名称
     * @return 网关节点
     */
    public GatewayNode createGatewayNode(String name) {
        return createGatewayNode(name, 1); // 默认并行网关
    }
    
    /**
     * 创建网关节点
     * @param name 节点名称
     * @param gatewayType 网关类型（1-并行，2-唯一）
     * @return 网关节点
     */
    public GatewayNode createGatewayNode(String name, Integer gatewayType) {
        GatewayNode node = new GatewayNode();
        node.setId(generateNodeId());
        node.setName(name != null ? name : "分支");
        node.setTypeId(1);
        node.setDesc("");
        node.setAlias("");
        node.setPrveId("");
        node.setNextId("");
        node.setSelectNodeId("");
        node.setSelectNodeName("");
        node.setIsException(false);
        node.setGatewayType(gatewayType != null ? gatewayType : 1);
        node.setFlowIds(new ArrayList<>());
        
        log.debug("创建网关节点: {}, 类型: {}", node.getId(), gatewayType);
        return node;
    }
    
    /**
     * 创建条件节点（分支叶子）
     * @param name 节点名称
     * @return 条件节点
     */
    public ConditionNode createConditionNode(String name) {
        ConditionNode node = new ConditionNode();
        node.setId(generateNodeId());
        node.setName(name != null ? name : "");
        node.setTypeId(2);
        node.setDesc("");
        node.setAlias("");
        node.setPrveId("");
        node.setNextId("");
        node.setSelectNodeId("");
        node.setSelectNodeName("");
        node.setIsException(false);
        node.setOperateCondition(new ArrayList<>());
        
        log.debug("创建条件节点: {}", node.getId());
        return node;
    }
    
    /**
     * 创建审批节点
     * @param name 节点名称
     * @return 审批节点
     */
    public ApprovalNode createApprovalNode(String name) {
        ApprovalNode node = new ApprovalNode();
        node.setId(generateNodeId());
        node.setName(name != null ? name : "审批");
        node.setTypeId(4);
        node.setDesc("");
        node.setAlias("");
        node.setPrveId("");
        node.setNextId("");
        node.setSelectNodeId("");
        node.setSelectNodeName("");
        node.setIsException(false);
        node.setAccounts(new ArrayList<>());
        
        log.debug("创建审批节点: {}", node.getId());
        return node;
    }
    
    /**
     * 创建审批流程节点（包含子流程）
     * @param name 节点名称
     * @return 审批流程节点
     */
    public ApprovalProcessNode createApprovalProcessNode(String name) {
        ApprovalProcessNode node = new ApprovalProcessNode();
        node.setId(generateNodeId());
        node.setName(name != null ? name : "审批流程");
        node.setTypeId(26);
        node.setDesc("");
        node.setAlias("");
        node.setPrveId("");
        node.setNextId("");
        node.setSelectNodeId("");
        node.setSelectNodeName("");
        node.setIsException(false);
        node.setAccounts(new ArrayList<>());
        node.setFormProperties(new ArrayList<>());
        
        log.debug("创建审批流程节点: {}", node.getId());
        return node;
    }
    
    /**
     * 创建填写节点
     * @param name 节点名称
     * @return 填写节点
     */
    public WriteNode createWriteNode(String name) {
        WriteNode node = new WriteNode();
        node.setId(generateNodeId());
        node.setName(name != null ? name : "填写");
        node.setTypeId(3);
        node.setDesc("");
        node.setAlias("");
        node.setPrveId("");
        node.setNextId("");
        node.setSelectNodeId("");
        node.setSelectNodeName("");
        node.setIsException(false);
        node.setAccounts(new ArrayList<>());
        node.setFormProperties(new ArrayList<>());
        
        log.debug("创建填写节点: {}", node.getId());
        return node;
    }
    
    /**
     * 创建抄送节点
     * @param name 节点名称
     * @return 抄送节点
     */
    public CcNode createCcNode(String name) {
        CcNode node = new CcNode();
        node.setId(generateNodeId());
        node.setName(name != null ? name : "抄送");
        node.setTypeId(5);
        node.setDesc("");
        node.setAlias("");
        node.setPrveId("");
        node.setNextId("");
        node.setSelectNodeId("");
        node.setSelectNodeName("");
        node.setIsException(false);
        node.setAccounts(new ArrayList<>());
        
        log.debug("创建抄送节点: {}", node.getId());
        return node;
    }
    
    /**
     * 创建通知节点
     * @param name 节点名称
     * @return 通知节点
     */
    public NotifyNode createNotifyNode(String name) {
        NotifyNode node = new NotifyNode();
        node.setId(generateNodeId());
        node.setName(name != null ? name : "发送站内通知");
        node.setTypeId(27);
        node.setDesc("");
        node.setAlias("");
        node.setPrveId("");
        node.setNextId("");
        node.setSelectNodeId("");
        node.setSelectNodeName("");
        node.setIsException(false);
        node.setAccounts(new ArrayList<>());
        
        log.debug("创建通知节点: {}", node.getId());
        return node;
    }
    
    /**
     * 创建获取更多记录节点
     * @param name 节点名称
     * @return 获取更多记录节点
     */
    public GetMoreRecordNode createGetMoreRecordNode(String name) {
        GetMoreRecordNode node = new GetMoreRecordNode();
        node.setId(generateNodeId());
        node.setName(name != null ? name : "获取更多记录");
        node.setTypeId(6);
        node.setDesc("");
        node.setAlias("");
        node.setPrveId("");
        node.setNextId("");
        node.setSelectNodeId("");
        node.setSelectNodeName("");
        node.setIsException(false);
        
        log.debug("创建获取更多记录节点: {}", node.getId());
        return node;
    }
    
    /**
     * 创建子流程节点
     * @param name 节点名称
     * @return 子流程节点
     */
    public SubProcessNode createSubProcessNode(String name) {
        SubProcessNode node = new SubProcessNode();
        node.setId(generateNodeId());
        node.setName(name != null ? name : "子流程");
        node.setTypeId(100); // 假设子流程的typeId是100
        node.setDesc("");
        node.setAlias("");
        node.setPrveId("");
        node.setNextId("");
        node.setSelectNodeId("");
        node.setSelectNodeName("");
        node.setIsException(false);
        
        log.debug("创建子流程节点: {}", node.getId());
        return node;
    }
    
    /**
     * 创建系统节点
     * @param name 节点名称
     * @return 系统节点
     */
    public SystemNode createSystemNode(String name) {
        SystemNode node = new SystemNode();
        node.setId(generateNodeId());
        node.setName(name != null ? name : "系统");
        node.setTypeId(100);
        node.setDesc("");
        node.setAlias("");
        
        log.debug("创建系统节点: {}", node.getId());
        return node;
    }
    
    /**
     * 创建空的流程节点
     * @param name 流程名称
     * @return 流程节点
     */
    public ProcessNode createProcessNode(String name) {
        ProcessNode processNode = new ProcessNode();
        processNode.setId(generateNodeId());
        processNode.setFlowNodeMap(new HashMap<>());
        processNode.setChild(false);
        processNode.setExecIds(new ArrayList<>());
        processNode.setExecPendingIds(new ArrayList<>());
        
        // 创建默认的开始事件
        StartEventNode startEvent = createStartEventNode("开始事件");
        processNode.setStartEventId(startEvent.getId());
        processNode.getFlowNodeMap().put(startEvent.getId(), startEvent);
        
        log.debug("创建流程节点: {}, 开始事件: {}", processNode.getId(), startEvent.getId());
        return processNode;
    }
    
    /**
     * 创建默认的网关和分支叶子组合
     * @param gatewayName 网关名称
     * @param gatewayType 网关类型
     * @param branchCount 分支数量
     * @return 网关节点（已包含分支叶子）
     */
    public GatewayNode createGatewayWithBranches(String gatewayName, Integer gatewayType, int branchCount) {
        GatewayNode gateway = createGatewayNode(gatewayName, gatewayType);
        
        for (int i = 0; i < branchCount; i++) {
            ConditionNode branchLeaf = createConditionNode("");
            branchLeaf.setPrveId(gateway.getId());
            gateway.getFlowIds().add(branchLeaf.getId());
        }
        
        log.debug("创建网关 {} 包含 {} 个分支", gateway.getId(), branchCount);
        return gateway;
    }
}
