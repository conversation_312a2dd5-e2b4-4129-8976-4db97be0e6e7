{"id": "6774e5fcd41c9043e0df39fb", "startEventId": "65e986df8a2bd83d79a5e557", "flowNodeMap": {"65e9878c7cd0e536c5e0b268": {"id": "65e9878c7cd0e536c5e0b268", "typeId": 27, "name": "发送站内通知", "desc": "", "alias": "", "prveId": "65e9874f57c8d64b906944b5", "nextId": "65e9880246009e42ac67ac76", "selectNodeId": "", "selectNodeName": "", "accounts": [{"type": 6, "entityId": "65e986df8a2bd83d79a5e557", "entityName": "工作表事件触发", "roleId": "65e578273d6b805db5347fc4", "roleTypeId": 0, "roleName": "用车人", "avatar": "", "count": 0, "controlType": 26, "flowNodeType": 0, "appType": 1}], "isException": false}, "5d39140d381d42d20db0c4da": {"id": "5d39140d381d42d20db0c4da", "typeId": 100, "name": "系统", "desc": "", "alias": ""}, "68bc27716c965f0d84818f5d": {"id": "68bc27716c965f0d84818f5d", "typeId": 27, "name": "通知下级部门", "desc": "", "alias": "", "prveId": "65e9874f57c8d64b906944b4", "nextId": "99", "selectNodeId": "", "selectNodeName": "", "accounts": [{"type": 8, "entityId": "4839502b-747e-41bb-9d2b-64005b1e27d7", "entityName": "客户端组", "roleId": "", "roleName": "", "avatar": "", "count": 1, "includeSub": true, "controlType": 0, "actionId": "", "appType": -1}], "isException": false}, "65e986df8a2bd83d79a5e557": {"id": "65e986df8a2bd83d79a5e557", "typeId": 0, "name": "工作表事件触发", "desc": "", "alias": "", "nextId": "65e986ef7cd0e536c5e0367f", "appType": 1, "appName": "用车申请", "appId": "6774e60a062f0c290ee79a37", "assignFieldNames": ["重新审批", "车辆分配审批"], "selectNodeId": "65e986df8a2bd83d79a5e557", "selectNodeName": "工作表事件触发", "isException": false}, "65e987d17cd0e536c5e104d6": {"id": "65e987d17cd0e536c5e104d6", "typeId": 27, "name": "发送站内通知", "desc": "", "alias": "", "prveId": "65e9874f57c8d64b906944b6", "nextId": "", "selectNodeId": "", "selectNodeName": "", "accounts": [{"type": 6, "entityId": "65e986df8a2bd83d79a5e557", "entityName": "工作表事件触发", "roleId": "65e578273d6b805db5347fc4", "roleTypeId": 0, "roleName": "用车人", "avatar": "", "count": 0, "controlType": 26, "flowNodeType": 0, "appType": 1}], "isException": false}, "643386f31774b22055074673": {"id": "643386f31774b22055074673", "typeId": 13, "name": "人工节点操作明细", "desc": "", "alias": "", "nextId": "", "appType": 101, "actionId": "405", "selectNodeId": "", "selectNodeName": "", "isException": true, "execute": false}, "65e9880246009e42ac67ac76": {"id": "65e9880246009e42ac67ac76", "typeId": 5, "name": "抄送", "desc": "", "alias": "", "prveId": "65e9878c7cd0e536c5e0b268", "nextId": "", "selectNodeId": "65e986df8a2bd83d79a5e557", "selectNodeName": "工作表事件触发", "accounts": [{"type": 6, "entityId": "65e986df8a2bd83d79a5e557", "entityName": "工作表事件触发", "roleId": "65e578273d6b805db5347fc4", "roleTypeId": 0, "roleName": "用车人", "avatar": "", "count": 0, "controlType": 26, "flowNodeType": 0, "appType": 1}], "isException": false}, "65e9874f57c8d64b906944b4": {"id": "65e9874f57c8d64b906944b4", "typeId": 1, "name": "分支", "desc": "", "alias": "", "prveId": "65e986ef7cd0e536c5e0367f", "nextId": "68bc27716c965f0d84818f5d", "flowIds": ["65e9874f57c8d64b906944b5", "65e9874f57c8d64b906944b6"], "selectNodeId": "", "selectNodeName": "", "gatewayType": 2}, "65e9874f57c8d64b906944b5": {"id": "65e9874f57c8d64b906944b5", "typeId": 2, "name": "", "desc": "", "alias": "", "prveId": "65e9874f57c8d64b906944b4", "nextId": "65e9878c7cd0e536c5e0b268", "operateCondition": [[{"nodeId": "65e986ef7cd0e536c5e0367f", "nodeName": "未命名审批流程", "nodeType": 26, "appType": 10, "actionId": "", "filedId": "result", "filedValue": "审批结果", "sourceType": 0, "advancedSetting": null, "filedTypeId": 11, "enumDefault": 0, "conditionId": "9", "value": null, "conditionValues": [{"nodeId": "", "nodeName": "", "nodeType": null, "appType": null, "actionId": "", "controlId": null, "controlName": "", "sourceType": null, "value": {"key": "PASS", "value": "通过", "isDeleted": false, "score": null, "index": null}, "type": null}], "ignoreEmpty": null, "ignoreValueEmpty": null, "fromValue": null, "toValue": null}]], "selectNodeId": "", "selectNodeName": "", "isException": false}, "65e9874f57c8d64b906944b6": {"id": "65e9874f57c8d64b906944b6", "typeId": 2, "name": "", "desc": "", "alias": "", "prveId": "65e9874f57c8d64b906944b4", "nextId": "65e987d17cd0e536c5e104d6", "operateCondition": [[{"nodeId": "65e986ef7cd0e536c5e0367f", "nodeName": "未命名审批流程", "nodeType": 26, "appType": 10, "actionId": "", "filedId": "result", "filedValue": "审批结果", "sourceType": 0, "advancedSetting": null, "filedTypeId": 11, "enumDefault": 0, "conditionId": "10", "value": null, "conditionValues": [{"nodeId": "", "nodeName": "", "nodeType": null, "appType": null, "actionId": "", "controlId": null, "controlName": "", "sourceType": null, "value": {"key": "PASS", "value": "通过", "isDeleted": false, "score": null, "index": null}, "type": null}], "ignoreEmpty": null, "ignoreValueEmpty": null, "fromValue": null, "toValue": null}]], "selectNodeId": "", "selectNodeName": "", "isException": false}, "6038a1cbf18158039fb40e68": {"id": "6038a1cbf18158039fb40e68", "typeId": 100, "name": "本流程参数", "desc": "", "alias": ""}, "65e986ef7cd0e536c5e0367f": {"id": "65e986ef7cd0e536c5e0367f", "typeId": 26, "name": "未命名审批流程", "desc": "", "alias": "", "prveId": "65e986df8a2bd83d79a5e557", "nextId": "65e9874f57c8d64b906944b4", "formProperties": [], "selectNodeId": "65e986df8a2bd83d79a5e557", "selectNodeName": "工作表事件触发", "accounts": [{"type": 6, "entityId": "65e986df8a2bd83d79a5e557", "entityName": "工作表事件触发", "roleId": "65e578273d6b805db5347fc4", "roleTypeId": 0, "roleName": "用车人", "avatar": "", "count": 0, "controlType": 26, "flowNodeType": 0, "appType": 1}], "isException": false, "sourceAppId": "6774e60a062f0c290ee79a37", "processNode": {"id": "6774e5fcd41c9043e0df39ec", "startEventId": "65e986ef7cd0e536c5e03681", "flowNodeMap": {"643386f31774b22055074673": {"id": "643386f31774b22055074673", "typeId": 13, "name": "人工节点操作明细", "desc": "", "alias": "", "nextId": "", "appType": 101, "actionId": "405", "selectNodeId": "", "selectNodeName": "", "isException": true, "execute": false}, "65e986ef7cd0e536c5e03681": {"id": "65e986ef7cd0e536c5e03681", "typeId": 0, "name": "发起审批", "desc": "", "alias": "", "nextId": "65e986fa09354d0811e1150a", "appType": 9, "appName": "用车申请", "appId": "6774e60a062f0c290ee79a37", "triggerId": "6774e5fcd41c9043e0df39fb", "triggerName": "车辆分配审批-子流程", "triggerNodeId": "65e986ef7cd0e536c5e0367f", "selectNodeId": "65e986ef7cd0e536c5e03681", "selectNodeName": "发起审批", "accounts": [{"type": 6, "entityId": "65e986df8a2bd83d79a5e557", "entityName": "工作表事件触发", "roleId": "65e578273d6b805db5347fc4", "roleTypeId": 0, "roleName": "用车人", "avatar": "", "count": 0, "controlType": 26, "flowNodeType": 0, "appType": 1}], "isException": false}, "5d39140d381d42d20db0c4da": {"id": "5d39140d381d42d20db0c4da", "typeId": 100, "name": "系统", "desc": "", "alias": ""}, "65e986fa09354d0811e1150a": {"id": "65e986fa09354d0811e1150a", "typeId": 4, "name": "审批", "desc": "", "alias": "", "prveId": "65e986ef7cd0e536c5e03681", "nextId": "99", "selectNodeId": "65e986ef7cd0e536c5e03681", "selectNodeName": "发起审批", "accounts": [{"type": 6, "entityId": "65e986ef7cd0e536c5e03681", "entityName": "发起审批", "roleId": "65e578273d6b805db5347fc4", "roleTypeId": 0, "roleName": "用车人", "avatar": "", "count": 0, "controlType": 26, "flowNodeType": 0, "appType": 9}], "countersignType": 0, "countersign": false, "multipleLevelType": 0, "isException": false, "isCallBack": true}, "6038a1cbf18158039fb40e68": {"id": "6038a1cbf18158039fb40e68", "typeId": 100, "name": "本流程参数", "desc": "", "alias": ""}, "6038a1cbf18158039fb40e69": {"id": "6038a1cbf18158039fb40e69", "typeId": 100, "name": "全局变量", "desc": "", "alias": ""}}, "child": false, "execIds": [], "execPendingIds": []}}, "6038a1cbf18158039fb40e69": {"id": "6038a1cbf18158039fb40e69", "typeId": 100, "name": "全局变量", "desc": "", "alias": ""}}, "child": true, "execIds": [], "execPendingIds": []}