package com.mlc.workflow.core.editor.structure.autowire;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.BaseNode;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.query.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.endowner.EndOwnerManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 通用自动连线策略
 * 实现设计方案中的核心连线原语：Detach、SpliceBetween、Replace、ConnectToEnd等
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class AutoWireStrategy {
    
    @Autowired
    private WorkflowQueryService queryService;

    @Autowired
    private EndOwnerManager endOwnerManager;
    
    /**
     * 断开操作：将指定的链段从流程中断开
     * @param processNode 流程节点
     * @param head 链段头节点
     * @param tail 链段尾节点
     * @return 断开操作的上下文信息
     */
    public DetachContext detach(ProcessNode processNode, BaseNode head, BaseNode tail) {
        if (processNode == null || head == null || tail == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        DetachContext context = new DetachContext();
        
        // 找到所有指向head的前驱节点
        List<BaseNode> prevNodes = queryService.findPrevNodes(processNode, head.getId());
        context.setPrevNodes(prevNodes);
        
        // 保存tail的下一个节点
        if (tail instanceof IRoutable routableTail) {
            context.setOriginalNext(routableTail.getNextId());
        }
        
        // 断开前驱节点的连接
        for (BaseNode prevNode : prevNodes) {
            if (prevNode instanceof IRoutable routablePrev) {
                routablePrev.setNextId(null); // 暂置为null
            }
        }
        
        // 断开tail的连接
        if (tail instanceof IRoutable routableTail) {
            routableTail.setNextId(null);
        }
        
        log.debug("断开链段 {} -> {}, 前驱节点数: {}", head.getId(), tail.getId(), prevNodes.size());
        
        return context;
    }
    
    /**
     * 拼接操作：将链段拼接到指定位置
     * @param processNode 流程节点
     * @param prevNodes 前驱节点列表
     * @param head 链段头节点
     * @param tail 链段尾节点
     * @param nextNodeId 下一个节点ID
     */
    public void spliceBetween(ProcessNode processNode, List<BaseNode> prevNodes, 
                            BaseNode head, BaseNode tail, String nextNodeId) {
        if (processNode == null || head == null || tail == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        // 连接前驱节点到head
        if (prevNodes != null) {
            for (BaseNode prevNode : prevNodes) {
                if (prevNode instanceof IRoutable routablePrev) {
                    routablePrev.setNextId(head.getId());
                }
            }
        }
        
        // 连接tail到下一个节点
        if (tail instanceof IRoutable routableTail) {
            routableTail.setNextId(nextNodeId);
        }
        
        log.debug("拼接链段 {} -> {} 到位置，前驱节点数: {}, 下一个节点: {}", 
                head.getId(), tail.getId(), prevNodes != null ? prevNodes.size() : 0, nextNodeId);
    }
    
    /**
     * 替换操作：用新链段替换旧链段
     * @param processNode 流程节点
     * @param oldHead 旧链段头节点
     * @param oldTail 旧链段尾节点
     * @param newHead 新链段头节点
     * @param newTail 新链段尾节点
     */
    public void replace(ProcessNode processNode, BaseNode oldHead, BaseNode oldTail, 
                       BaseNode newHead, BaseNode newTail) {
        if (processNode == null || oldHead == null || oldTail == null || 
            newHead == null || newTail == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        // 断开旧链段
        DetachContext detachContext = detach(processNode, oldHead, oldTail);
        
        // 拼接新链段
        spliceBetween(processNode, detachContext.getPrevNodes(), newHead, newTail, 
                     detachContext.getOriginalNext());
        
        log.debug("替换链段 {} -> {} 为 {} -> {}", 
                oldHead.getId(), oldTail.getId(), newHead.getId(), newTail.getId());
    }
    
    /**
     * 连接到结束：将节点连接到流程结束
     * @param processNode 流程节点
     * @param tail 尾节点
     */
    public void connectToEnd(ProcessNode processNode, BaseNode tail) {
        EndOwnerManager.EndOwnerOperationResult result = endOwnerManager.connectToEnd(processNode, tail);
        if (!result.isSuccess()) {
            log.warn("连接到结束失败: {}", result.getErrors());
        }
    }
    
    /**
     * 查找EndOwner节点
     * @param processNode 流程节点
     * @return EndOwner节点，如果没有则返回null
     */
    public BaseNode findEndOwner(ProcessNode processNode) {
        return endOwnerManager.findEndOwner(processNode);
    }
    
    /**
     * 修复EndOwner：当结构扁平化导致EndOwner不再是实际末尾时
     * @param processNode 流程节点
     */
    public void abortEndOwnerIfFlatten(ProcessNode processNode) {
        EndOwnerManager.EndOwnerOperationResult result = endOwnerManager.repairEndOwnerAfterFlatten(processNode);
        if (!result.isSuccess()) {
            log.warn("修复EndOwner失败: {}", result.getErrors());
        }
    }
    
    /**
     * 断开操作的上下文信息
     */
    public static class DetachContext {
        private List<BaseNode> prevNodes;
        private String originalNext;
        
        public List<BaseNode> getPrevNodes() {
            return prevNodes;
        }
        
        public void setPrevNodes(List<BaseNode> prevNodes) {
            this.prevNodes = prevNodes;
        }
        
        public String getOriginalNext() {
            return originalNext;
        }
        
        public void setOriginalNext(String originalNext) {
            this.originalNext = originalNext;
        }
    }
}
