package com.mlc.workflow.core.editor.structure.subprocess;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.BaseNode;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IHasSubProcess;
import com.mlc.workflow.core.editor.structure.WorkflowValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 子流程管理器
 * 处理子流程的递归操作和一致性维护
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class SubProcessManager {
    
    @Autowired
    private WorkflowValidator validator;
    
    /**
     * 递归验证所有子流程
     * @param processNode 主流程节点
     * @return 验证结果
     */
    public WorkflowValidator.ValidationResult validateAllSubProcesses(ProcessNode processNode) {
        WorkflowValidator.ValidationResult result = new WorkflowValidator.ValidationResult();
        
        if (processNode == null) {
            result.addError("流程节点不能为空");
            return result;
        }
        
        // 验证主流程
        WorkflowValidator.ValidationResult mainResult = validator.validate(processNode);
        if (!mainResult.isValid()) {
            result.getErrors().addAll(mainResult.getErrors());
        }
        result.getWarnings().addAll(mainResult.getWarnings());
        
        // 递归验证所有子流程
        validateSubProcessesRecursive(processNode, result, new HashSet<>());
        
        return result;
    }
    
    /**
     * 递归验证子流程
     * @param processNode 当前流程节点
     * @param result 验证结果
     * @param visitedProcesses 已访问的流程集合（防止循环引用）
     */
    private void validateSubProcessesRecursive(ProcessNode processNode, 
                                             WorkflowValidator.ValidationResult result,
                                             Set<String> visitedProcesses) {
        if (processNode == null || visitedProcesses.contains(processNode.getId())) {
            return;
        }
        
        visitedProcesses.add(processNode.getId());
        
        Map<String, BaseNode> flowNodeMap = processNode.getFlowNodeMap();
        if (flowNodeMap == null) {
            return;
        }
        
        for (BaseNode node : flowNodeMap.values()) {
            if (node instanceof IHasSubProcess subProcessNode) {
                ProcessNode subProcess = subProcessNode.getProcessNode();
                if (subProcess != null) {
                    // 验证子流程
                    WorkflowValidator.ValidationResult subResult = validator.validate(subProcess);
                    if (!subResult.isValid()) {
                        result.addError("子流程 " + node.getId() + " 验证失败: " + 
                                      String.join(", ", subResult.getErrors()));
                    }
                    result.getWarnings().addAll(subResult.getWarnings());
                    
                    // 递归验证子流程的子流程
                    validateSubProcessesRecursive(subProcess, result, visitedProcesses);
                }
            }
        }
    }
    
    /**
     * 查找所有子流程节点
     * @param processNode 流程节点
     * @return 子流程节点列表
     */
    public List<BaseNode> findAllSubProcessNodes(ProcessNode processNode) {
        List<BaseNode> subProcessNodes = new ArrayList<>();
        
        if (processNode == null) {
            return subProcessNodes;
        }
        
        findSubProcessNodesRecursive(processNode, subProcessNodes, new HashSet<>());
        
        return subProcessNodes;
    }
    
    /**
     * 递归查找子流程节点
     * @param processNode 当前流程节点
     * @param subProcessNodes 子流程节点列表
     * @param visitedProcesses 已访问的流程集合
     */
    private void findSubProcessNodesRecursive(ProcessNode processNode, 
                                            List<BaseNode> subProcessNodes,
                                            Set<String> visitedProcesses) {
        if (processNode == null || visitedProcesses.contains(processNode.getId())) {
            return;
        }
        
        visitedProcesses.add(processNode.getId());
        
        Map<String, BaseNode> flowNodeMap = processNode.getFlowNodeMap();
        if (flowNodeMap == null) {
            return;
        }
        
        for (BaseNode node : flowNodeMap.values()) {
            if (node instanceof IHasSubProcess subProcessNode) {
                subProcessNodes.add(node);
                
                ProcessNode subProcess = subProcessNode.getProcessNode();
                if (subProcess != null) {
                    // 递归查找子流程的子流程
                    findSubProcessNodesRecursive(subProcess, subProcessNodes, visitedProcesses);
                }
            }
        }
    }
    
    /**
     * 克隆子流程（深拷贝）
     * @param originalSubProcess 原始子流程
     * @return 克隆的子流程
     */
    public ProcessNode cloneSubProcess(ProcessNode originalSubProcess) {
        if (originalSubProcess == null) {
            return null;
        }
        
        ProcessNode clonedProcess = new ProcessNode();
        clonedProcess.setId(generateNewProcessId());
        clonedProcess.setStartEventId(originalSubProcess.getStartEventId());
        
        Map<String, BaseNode> clonedNodeMap = new HashMap<>();
        Map<String, String> idMapping = new HashMap<>();
        
        // 第一遍：克隆所有节点并建立ID映射
        for (Map.Entry<String, BaseNode> entry : originalSubProcess.getFlowNodeMap().entrySet()) {
            String originalId = entry.getKey();
            BaseNode originalNode = entry.getValue();
            
            BaseNode clonedNode = cloneNode(originalNode);
            String newId = generateNewNodeId();
            clonedNode.setId(newId);
            
            idMapping.put(originalId, newId);
            clonedNodeMap.put(newId, clonedNode);
        }
        
        // 第二遍：更新所有引用关系
        updateNodeReferences(clonedNodeMap, idMapping);
        
        // 更新开始事件ID
        if (originalSubProcess.getStartEventId() != null) {
            String newStartEventId = idMapping.get(originalSubProcess.getStartEventId());
            clonedProcess.setStartEventId(newStartEventId);
        }
        
        clonedProcess.setFlowNodeMap(clonedNodeMap);
        
        log.debug("克隆子流程: {} -> {}", originalSubProcess.getId(), clonedProcess.getId());
        
        return clonedProcess;
    }
    
    /**
     * 克隆单个节点（简单实现）
     * @param originalNode 原始节点
     * @return 克隆的节点
     */
    private BaseNode cloneNode(BaseNode originalNode) {
        // 这里应该实现完整的节点深拷贝
        // 为了简化，这里只是创建一个新的同类型节点并复制基本属性
        try {
            BaseNode clonedNode = originalNode.getClass().getDeclaredConstructor().newInstance();
            clonedNode.setName(originalNode.getName());
            clonedNode.setDesc(originalNode.getDesc());
            clonedNode.setAlias(originalNode.getAlias());
            clonedNode.setTypeId(originalNode.getTypeId());
            
            // 这里应该复制更多特定类型的属性
            // 实际应用中需要为每种节点类型实现完整的克隆逻辑
            
            return clonedNode;
        } catch (Exception e) {
            log.error("克隆节点失败: " + originalNode.getClass().getSimpleName(), e);
            throw new RuntimeException("克隆节点失败", e);
        }
    }
    
    /**
     * 更新节点引用关系
     * @param clonedNodeMap 克隆的节点映射
     * @param idMapping ID映射关系
     */
    private void updateNodeReferences(Map<String, BaseNode> clonedNodeMap, Map<String, String> idMapping) {
        for (BaseNode node : clonedNodeMap.values()) {
            // 更新路由引用
            if (node instanceof com.mlc.workflow.core.editor.runtime.nodes.capability.IRoutable routableNode) {
                String oldNextId = routableNode.getNextId();
                if (oldNextId != null && idMapping.containsKey(oldNextId)) {
                    routableNode.setNextId(idMapping.get(oldNextId));
                }
                
                String oldPrveId = routableNode.getPrveId();
                if (oldPrveId != null && idMapping.containsKey(oldPrveId)) {
                    routableNode.setPrveId(idMapping.get(oldPrveId));
                }
            }
            
            // 更新分支引用
            if (node instanceof com.mlc.workflow.core.editor.runtime.nodes.capability.IHasBranches branchNode) {
                List<String> oldFlowIds = branchNode.getFlowIds();
                if (oldFlowIds != null) {
                    List<String> newFlowIds = new ArrayList<>();
                    for (String oldFlowId : oldFlowIds) {
                        String newFlowId = idMapping.get(oldFlowId);
                        if (newFlowId != null) {
                            newFlowIds.add(newFlowId);
                        }
                    }
                    branchNode.setFlowIds(newFlowIds);
                }
            }
            
            // 更新子流程引用（递归克隆）
            if (node instanceof IHasSubProcess subProcessNode) {
                ProcessNode originalSubProcess = subProcessNode.getProcessNode();
                if (originalSubProcess != null) {
                    ProcessNode clonedSubProcess = cloneSubProcess(originalSubProcess);
                    subProcessNode.setProcessNode(clonedSubProcess);
                }
            }
        }
    }
    
    /**
     * 删除子流程及其所有内容
     * @param subProcessNode 子流程节点
     */
    public void deleteSubProcess(BaseNode subProcessNode) {
        if (!(subProcessNode instanceof IHasSubProcess hasSubProcess)) {
            return;
        }
        
        ProcessNode subProcess = hasSubProcess.getProcessNode();
        if (subProcess == null) {
            return;
        }
        
        log.debug("删除子流程: {}", subProcess.getId());
        
        // 递归删除所有嵌套的子流程
        Map<String, BaseNode> flowNodeMap = subProcess.getFlowNodeMap();
        if (flowNodeMap != null) {
            for (BaseNode node : flowNodeMap.values()) {
                if (node instanceof IHasSubProcess nestedSubProcess) {
                    deleteSubProcess(node);
                }
            }
        }
        
        // 清空子流程引用
        hasSubProcess.setProcessNode(null);
    }
    
    /**
     * 生成新的流程ID
     * @return 新的流程ID
     */
    private String generateNewProcessId() {
        return "process-" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
    
    /**
     * 生成新的节点ID
     * @return 新的节点ID
     */
    private String generateNewNodeId() {
        return "node-" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
    
    /**
     * 检查子流程循环引用
     * @param processNode 流程节点
     * @return 是否存在循环引用
     */
    public boolean hasCircularReference(ProcessNode processNode) {
        Set<String> visitedProcesses = new HashSet<>();
        return hasCircularReferenceRecursive(processNode, visitedProcesses);
    }
    
    /**
     * 递归检查循环引用
     * @param processNode 当前流程节点
     * @param visitedProcesses 已访问的流程集合
     * @return 是否存在循环引用
     */
    private boolean hasCircularReferenceRecursive(ProcessNode processNode, Set<String> visitedProcesses) {
        if (processNode == null) {
            return false;
        }
        
        String processId = processNode.getId();
        if (visitedProcesses.contains(processId)) {
            return true; // 发现循环引用
        }
        
        visitedProcesses.add(processId);
        
        Map<String, BaseNode> flowNodeMap = processNode.getFlowNodeMap();
        if (flowNodeMap != null) {
            for (BaseNode node : flowNodeMap.values()) {
                if (node instanceof IHasSubProcess subProcessNode) {
                    ProcessNode subProcess = subProcessNode.getProcessNode();
                    if (hasCircularReferenceRecursive(subProcess, new HashSet<>(visitedProcesses))) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
}
