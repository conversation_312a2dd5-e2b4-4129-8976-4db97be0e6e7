package com.mlc.workflow.core.editor.structure.command;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;
import com.mlc.workflow.core.editor.runtime.nodes.*;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.factory.NodeFactory;
import com.mlc.workflow.core.editor.structure.strategy.GatewaySemanticsStrategy;
import com.mlc.workflow.core.editor.structure.query.WorkflowQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 网关操作命令
 * 实现网关的新增、删除、类型切换等操作
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class GatewayOperations {
    
    @Autowired
    private NodeFactory nodeFactory;
    
    @Autowired
    private AutoWireStrategy autoWireStrategy;
    
    @Autowired
    private GatewaySemanticsStrategy gatewaySemantics;
    
    @Autowired
    private WorkflowQueryService queryService;
    
    /**
     * 放置策略枚举
     */
    public enum PlacementStrategy {
        LEFT_PLACEMENT,  // 左侧放置（移动原有链段到左分支）
        NO_MOVE         // 不移动（在原位置插入网关）
    }
    
    /**
     * 新增网关（默认并行）
     * @param processNode 流程节点
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @return 创建的网关节点
     */
    public GatewayNode addGateway(ProcessNode processNode, String atNodeId, PlacementStrategy placement) {
        return addGateway(processNode, atNodeId, placement, GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL);
    }
    
    /**
     * 新增网关
     * @param processNode 流程节点
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @param gatewayType 网关类型
     * @return 创建的网关节点
     */
    public GatewayNode addGateway(ProcessNode processNode, String atNodeId, 
                                PlacementStrategy placement, Integer gatewayType) {
        if (processNode == null || atNodeId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        BaseNode atNode = processNode.getFlowNodeMap().get(atNodeId);
        if (atNode == null) {
            throw new IllegalArgumentException("找不到插入点节点: " + atNodeId);
        }
        
        if (!(atNode instanceof IRoutable)) {
            throw new IllegalArgumentException("插入点节点必须是可路由的");
        }
        
        IRoutable routableAtNode = (IRoutable) atNode;
        String originalNextId = routableAtNode.getNextId();
        
        // 创建网关和分支叶子
        GatewayNode gateway = nodeFactory.createGatewayNode("分支", gatewayType);
        ConditionNode leftBranch = nodeFactory.createConditionNode("");
        ConditionNode rightBranch = nodeFactory.createConditionNode("");

        // 如果是唯一分支网关，为分支叶子设置默认条件
        if (gatewayType == GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE) {
            setDefaultConditionForBranch(leftBranch, 0, 2);
            setDefaultConditionForBranch(rightBranch, 1, 2);
        }

        // 设置网关的分支
        gateway.getFlowIds().add(leftBranch.getId());
        gateway.getFlowIds().add(rightBranch.getId());

        // 设置分支叶子的前驱
        leftBranch.setPrveId(gateway.getId());
        rightBranch.setPrveId(gateway.getId());
        
        // 添加到流程节点映射
        processNode.getFlowNodeMap().put(gateway.getId(), gateway);
        processNode.getFlowNodeMap().put(leftBranch.getId(), leftBranch);
        processNode.getFlowNodeMap().put(rightBranch.getId(), rightBranch);
        
        // 根据放置策略处理
        if (placement == PlacementStrategy.LEFT_PLACEMENT) {
            handleLeftPlacement(processNode, routableAtNode, gateway, leftBranch, rightBranch, originalNextId);
        } else {
            handleNoMove(processNode, routableAtNode, gateway, leftBranch, rightBranch, originalNextId);
        }
        
        log.debug("在节点 {} 后新增网关 {}，策略: {}", atNodeId, gateway.getId(), placement);
        
        return gateway;
    }
    
    /**
     * 处理左侧放置策略
     */
    private void handleLeftPlacement(ProcessNode processNode, IRoutable atNode, GatewayNode gateway,
                                   ConditionNode leftBranch, ConditionNode rightBranch, String originalNextId) {
        // 连接atNode到网关
        atNode.setNextId(gateway.getId());
        gateway.setPrveId(atNode.getId());
        
        if (originalNextId != null && !originalNextId.trim().isEmpty()) {
            if ("99".equals(originalNextId)) {
                // 原来指向结束，将左分支连接到结束
                leftBranch.setNextId("");
                autoWireStrategy.connectToEnd(processNode, leftBranch);
                gateway.setNextId(""); // 网关合流后无下游
            } else {
                // 将原有的下游链段移动到左分支
                leftBranch.setNextId(originalNextId);
                
                // 更新原下游节点的前驱
                BaseNode originalNext = processNode.getFlowNodeMap().get(originalNextId);
                if (originalNext instanceof IRoutable routableNext) {
                    routableNext.setPrveId(leftBranch.getId());
                }
                
                // 网关合流后继续原有流程
                gateway.setNextId(findBranchTailNext(processNode, originalNextId));
            }
        } else {
            // 原来没有下游
            leftBranch.setNextId("");
            gateway.setNextId("");
        }
        
        // 右分支设为空分支
        rightBranch.setNextId("");
    }
    
    /**
     * 处理不移动策略
     */
    private void handleNoMove(ProcessNode processNode, IRoutable atNode, GatewayNode gateway,
                            ConditionNode leftBranch, ConditionNode rightBranch, String originalNextId) {
        // 连接atNode到网关
        atNode.setNextId(gateway.getId());
        gateway.setPrveId(atNode.getId());
        
        // 网关合流后继续原有下游
        gateway.setNextId(originalNextId);
        
        // 更新原下游节点的前驱
        if (originalNextId != null && !originalNextId.trim().isEmpty() && !"99".equals(originalNextId)) {
            BaseNode originalNext = processNode.getFlowNodeMap().get(originalNextId);
            if (originalNext instanceof IRoutable routableNext) {
                routableNext.setPrveId(gateway.getId());
            }
        }
        
        // 两个分支都设为空分支（等待后续添加内容）
        leftBranch.setNextId("");
        rightBranch.setNextId("");
    }
    
    /**
     * 查找分支尾部的下一个节点
     */
    private String findBranchTailNext(ProcessNode processNode, String startNodeId) {
        Set<String> visited = new HashSet<>();
        return findBranchTailNextRecursive(processNode, startNodeId, visited);
    }
    
    private String findBranchTailNextRecursive(ProcessNode processNode, String currentNodeId, Set<String> visited) {
        if (currentNodeId == null || visited.contains(currentNodeId)) {
            return "";
        }
        
        visited.add(currentNodeId);
        BaseNode currentNode = processNode.getFlowNodeMap().get(currentNodeId);
        
        if (currentNode instanceof IRoutable routableNode) {
            String nextId = routableNode.getNextId();
            if (nextId == null || nextId.trim().isEmpty() || "99".equals(nextId)) {
                return nextId;
            }
            return findBranchTailNextRecursive(processNode, nextId, visited);
        }
        
        return "";
    }
    
    /**
     * 删除网关
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     */
    public void deleteGateway(ProcessNode processNode, String gatewayId) {
        if (processNode == null || gatewayId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        GatewayNode gateway = queryService.findGateway(processNode, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }
        
        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            throw new IllegalStateException("网关没有分支，无法删除");
        }
        
        if (flowIds.size() > 1) {
            throw new IllegalStateException("网关有多个分支，请先删除分支至只剩1条");
        }
        
        // 只剩一条分支，执行扁平化
        String remainingBranchId = flowIds.get(0);
        flattenGateway(processNode, gateway, remainingBranchId);
        
        log.debug("删除网关 {}，已扁平化", gatewayId);
    }
    
    /**
     * 扁平化网关（将单分支网关替换为分支链）
     */
    private void flattenGateway(ProcessNode processNode, GatewayNode gateway, String branchLeafId) {
        // 获取分支链
        List<BaseNode> branchChain = queryService.findBranchChain(processNode, branchLeafId);

        if (branchChain.isEmpty()) {
            throw new IllegalStateException("分支链为空");
        }

        BaseNode branchHead = branchChain.get(0);
        BaseNode branchTail = branchChain.get(branchChain.size() - 1);

        log.debug("扁平化网关 {}，分支链: {} -> {}", gateway.getId(), branchHead.getId(), branchTail.getId());

        // 使用AutoWireStrategy替换网关
        autoWireStrategy.replace(processNode, gateway, gateway, branchHead, branchTail);

        // 确保分支尾部正确连接到EndOwner
        autoWireStrategy.connectToEnd(processNode, branchTail);

        // 删除网关节点（分支链节点保留）
        processNode.getFlowNodeMap().remove(gateway.getId());

        log.debug("网关 {} 扁平化完成，保留分支链", gateway.getId());
        
        // 触发EndOwner检查
        autoWireStrategy.abortEndOwnerIfFlatten(processNode);
    }
    
    /**
     * 修改网关类型
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param toType 目标类型
     */
    public void switchGatewayType(ProcessNode processNode, String gatewayId, Integer toType) {
        if (processNode == null || gatewayId == null || toType == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        GatewayNode gateway = queryService.findGateway(processNode, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }
        
        // 使用网关语义策略处理类型切换
        gatewaySemantics.switchGatewayType(processNode, gateway, toType);
        
        log.debug("网关 {} 类型已切换为 {}", gatewayId, toType);
    }

    /**
     * 为分支设置默认条件
     * @param branch 分支节点
     * @param index 分支索引
     * @param totalBranches 总分支数
     */
    private void setDefaultConditionForBranch(ConditionNode branch, int index, int totalBranches) {
        if (branch.getOperateCondition() == null || branch.getOperateCondition().isEmpty()) {
            List<List<ConditionGroup>> defaultConditions = new ArrayList<>();
            List<ConditionGroup> conditionGroup = new ArrayList<>();

            // 创建一个简单的条件组
            ConditionGroup condition = new ConditionGroup();
            condition.setNodeId("system");  // 设置必需的 nodeId
            condition.setNodeName("系统");

            if (index == totalBranches - 1) {
                // 最后一个分支设为 else 条件
                condition.setFiledId("else");
                condition.setFiledValue("其他情况");
                condition.setConditionId("default_else");
                condition.setValue("else");
            } else {
                // 其他分支生成默认条件
                condition.setFiledId("condition_" + (index + 1));
                condition.setFiledValue("始终成立");
                condition.setConditionId("default_condition_" + (index + 1));
                condition.setValue("true");
            }

            conditionGroup.add(condition);
            defaultConditions.add(conditionGroup);
            branch.setOperateCondition(defaultConditions);

            log.debug("为分支 {} 生成默认条件: {}", branch.getId(), condition.getFiledId());
        }
    }
}
