package com.mlc.workflow.core.editor.structure.traverse;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.*;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IHasBranches;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IHasSubProcess;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IRoutable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 遍历服务
 * 实现DFS深度优先遍历，支持网关分支展开和子流程递归
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
public class TraverseService {
    
    /**
     * 遍历流程节点
     * @param processNode 流程节点
     * @param visitor 访问者
     */
    public void visit(ProcessNode processNode, NodeVisitor visitor) {
        if (processNode == null || visitor == null) {
            return;
        }
        
        TraverseContext context = new TraverseContext(processNode);
        try {
            visitor.startVisit(processNode, context);
            traverseFromStart(processNode, visitor, context);
        } catch (Exception e) {
            log.error("遍历流程时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("流程遍历失败", e);
        } finally {
            visitor.endVisit(processNode, context);
        }
    }
    
    /**
     * 从开始事件开始遍历
     * @param processNode 流程节点
     * @param visitor 访问者
     * @param context 遍历上下文
     */
    private void traverseFromStart(ProcessNode processNode, NodeVisitor visitor, TraverseContext context) {
        String startEventId = processNode.getStartEventId();
        if (startEventId == null || startEventId.trim().isEmpty()) {
            log.warn("流程 {} 没有开始事件ID", processNode.getId());
            return;
        }
        
        BaseNode startNode = processNode.getFlowNodeMap().get(startEventId);
        if (startNode == null) {
            log.warn("找不到开始事件节点: {}", startEventId);
            return;
        }
        
        traverseNode(startNode, visitor, context);
    }
    
    /**
     * 遍历单个节点
     * @param node 节点
     * @param visitor 访问者
     * @param context 遍历上下文
     */
    private void traverseNode(BaseNode node, NodeVisitor visitor, TraverseContext context) {
        if (node == null) {
            return;
        }
        
        String nodeId = node.getId();
        
        // 检查是否已访问（防止环路）
        if (context.isVisited(nodeId)) {
            log.warn("检测到环路，节点 {} 已被访问，当前路径: {}", nodeId, context.getCurrentPathString());
            return;
        }
        
        // 检查最大深度
        if (context.isMaxDepthExceeded()) {
            log.warn("超过最大遍历深度 {}，停止遍历", context.getMaxDepth());
            return;
        }
        
        // 标记为已访问
        context.markVisited(nodeId);
        
        try {
            // 访问当前节点
            visitNodeByType(node, visitor, context);
            
            // 继续遍历后续节点
            traverseNext(node, visitor, context);
            
        } finally {
            // 回溯时取消访问标记
            context.unmarkVisited(nodeId);
        }
    }
    
    /**
     * 根据节点类型访问节点
     * @param node 节点
     * @param visitor 访问者
     * @param context 遍历上下文
     */
    private void visitNodeByType(BaseNode node, NodeVisitor visitor, TraverseContext context) {
        if (node instanceof StartEventNode) {
            visitor.visitStartEvent((StartEventNode) node, context);
        } else if (node instanceof GatewayNode) {
            visitor.visitGateway((GatewayNode) node, context);
        } else if (node instanceof ConditionNode) {
            visitor.visitCondition((ConditionNode) node, context);
        } else if (node instanceof ApprovalNode) {
            visitor.visitApproval((ApprovalNode) node, context);
        } else if (node instanceof ApprovalProcessNode) {
            visitor.visitApprovalProcess((ApprovalProcessNode) node, context);
        } else if (node instanceof WriteNode) {
            visitor.visitWrite((WriteNode) node, context);
        } else if (node instanceof CcNode) {
            visitor.visitCc((CcNode) node, context);
        } else if (node instanceof NotifyNode) {
            visitor.visitNotify((NotifyNode) node, context);
        } else if (node instanceof GetMoreRecordNode) {
            visitor.visitGetMoreRecord((GetMoreRecordNode) node, context);
        } else if (node instanceof SubProcessNode) {
            visitor.visitSubProcess((SubProcessNode) node, context);
        } else if (node instanceof SystemNode) {
            visitor.visitSystem((SystemNode) node, context);
        } else if (node instanceof FlowNode) {
            visitor.visitFlowNode((FlowNode) node, context);
        }
    }
    
    /**
     * 遍历后续节点
     * @param node 当前节点
     * @param visitor 访问者
     * @param context 遍历上下文
     */
    private void traverseNext(BaseNode node, NodeVisitor visitor, TraverseContext context) {
        // 处理网关节点的分支
        if (node instanceof IHasBranches branchNode) {
            traverseBranches(branchNode, visitor, context);
            return;
        }
        
        // 处理子流程节点
        if (node instanceof IHasSubProcess subProcessNode) {
            traverseSubProcess(subProcessNode, visitor, context);
        }
        
        // 处理普通的下一个节点
        if (node instanceof IRoutable routableNode) {
            traverseNextNode(routableNode, visitor, context);
        }
    }
    
    /**
     * 遍历网关分支
     * @param branchNode 分支节点
     * @param visitor 访问者
     * @param context 遍历上下文
     */
    private void traverseBranches(IHasBranches branchNode, NodeVisitor visitor, TraverseContext context) {
        List<String> flowIds = branchNode.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            return;
        }
        
        ProcessNode processNode = context.getCurrentProcessNode();
        for (String flowId : flowIds) {
            BaseNode branchLeaf = processNode.getFlowNodeMap().get(flowId);
            if (branchLeaf != null) {
                traverseNode(branchLeaf, visitor, context);
            }
        }
        
        // 遍历网关的合流后节点
        if (branchNode instanceof IRoutable routableNode) {
            String nextId = routableNode.getNextId();
            if (nextId != null && !nextId.trim().isEmpty() && !"99".equals(nextId)) {
                BaseNode nextNode = processNode.getFlowNodeMap().get(nextId);
                if (nextNode != null) {
                    traverseNode(nextNode, visitor, context);
                }
            }
        }
    }
    
    /**
     * 遍历子流程
     * @param subProcessNode 子流程节点
     * @param visitor 访问者
     * @param context 遍历上下文
     */
    private void traverseSubProcess(IHasSubProcess subProcessNode, NodeVisitor visitor, TraverseContext context) {
        ProcessNode subProcess = subProcessNode.getProcessNode();
        if (subProcess != null) {
            context.enterSubProcess(subProcess);
            try {
                traverseFromStart(subProcess, visitor, context);
            } finally {
                context.exitSubProcess();
            }
        }
    }
    
    /**
     * 遍历下一个节点
     * @param routableNode 可路由节点
     * @param visitor 访问者
     * @param context 遍历上下文
     */
    private void traverseNextNode(IRoutable routableNode, NodeVisitor visitor, TraverseContext context) {
        String nextId = routableNode.getNextId();
        if (nextId == null || nextId.trim().isEmpty() || "99".equals(nextId)) {
            // 到达流程结束
            return;
        }
        
        ProcessNode processNode = context.getCurrentProcessNode();
        BaseNode nextNode = processNode.getFlowNodeMap().get(nextId);
        if (nextNode != null) {
            traverseNode(nextNode, visitor, context);
        } else {
            log.warn("找不到下一个节点: {}", nextId);
        }
    }
}
