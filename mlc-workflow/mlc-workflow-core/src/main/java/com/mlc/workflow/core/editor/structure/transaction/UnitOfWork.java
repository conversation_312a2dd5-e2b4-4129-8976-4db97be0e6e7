package com.mlc.workflow.core.editor.structure.transaction;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.BaseNode;
import com.mlc.workflow.core.editor.structure.WorkflowValidator;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 工作单元
 * 收集一次操作产生的所有写集，一次性提交，保证原子性与不变量
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class UnitOfWork {
    
    @Autowired
    private WorkflowValidator validator;
    
    /**
     * 操作类型枚举
     */
    public enum OperationType {
        CREATE,    // 创建节点
        UPDATE,    // 更新节点
        DELETE,    // 删除节点
        CONNECT    // 连接变更
    }
    
    /**
     * 变更记录
     */
    @Getter
    @Setter
    public static class ChangeRecord {
        private OperationType operationType;
        private String nodeId;
        private BaseNode oldNode;
        private BaseNode newNode;
        private Map<String, Object> metadata;
        
        public ChangeRecord(OperationType operationType, String nodeId) {
            this.operationType = operationType;
            this.nodeId = nodeId;
            this.metadata = new HashMap<>();
        }
    }
    
    /**
     * 工作单元上下文
     */
    private static class UnitOfWorkContext {
        private ProcessNode processNode;
        private List<ChangeRecord> changes;
        private Map<String, BaseNode> originalNodes;
        private boolean committed;
        private boolean rolledBack;
        
        public UnitOfWorkContext(ProcessNode processNode) {
            this.processNode = processNode;
            this.changes = new ArrayList<>();
            this.originalNodes = new HashMap<>();
            this.committed = false;
            this.rolledBack = false;
        }
    }
    
    // 使用ThreadLocal确保线程安全
    private final ThreadLocal<UnitOfWorkContext> contextHolder = new ThreadLocal<>();
    
    /**
     * 开始工作单元
     * @param processNode 流程节点
     */
    public void begin(ProcessNode processNode) {
        if (contextHolder.get() != null) {
            throw new IllegalStateException("工作单元已经开始，请先提交或回滚");
        }
        
        UnitOfWorkContext context = new UnitOfWorkContext(processNode);
        contextHolder.set(context);
        
        log.debug("开始工作单元，流程: {}", processNode.getId());
    }
    
    /**
     * 记录节点创建
     * @param node 新创建的节点
     */
    public void recordCreate(BaseNode node) {
        UnitOfWorkContext context = getCurrentContext();
        
        ChangeRecord record = new ChangeRecord(OperationType.CREATE, node.getId());
        record.setNewNode(node);
        context.changes.add(record);
        
        log.debug("记录创建节点: {}", node.getId());
    }
    
    /**
     * 记录节点更新
     * @param nodeId 节点ID
     * @param oldNode 原节点
     * @param newNode 新节点
     */
    public void recordUpdate(String nodeId, BaseNode oldNode, BaseNode newNode) {
        UnitOfWorkContext context = getCurrentContext();
        
        // 保存原始状态（如果还没保存）
        if (!context.originalNodes.containsKey(nodeId)) {
            context.originalNodes.put(nodeId, cloneNode(oldNode));
        }
        
        ChangeRecord record = new ChangeRecord(OperationType.UPDATE, nodeId);
        record.setOldNode(oldNode);
        record.setNewNode(newNode);
        context.changes.add(record);
        
        log.debug("记录更新节点: {}", nodeId);
    }
    
    /**
     * 记录节点删除
     * @param node 被删除的节点
     */
    public void recordDelete(BaseNode node) {
        UnitOfWorkContext context = getCurrentContext();
        
        ChangeRecord record = new ChangeRecord(OperationType.DELETE, node.getId());
        record.setOldNode(node);
        context.changes.add(record);
        
        log.debug("记录删除节点: {}", node.getId());
    }
    
    /**
     * 记录连接变更
     * @param nodeId 节点ID
     * @param metadata 连接变更的元数据
     */
    public void recordConnect(String nodeId, Map<String, Object> metadata) {
        UnitOfWorkContext context = getCurrentContext();
        
        ChangeRecord record = new ChangeRecord(OperationType.CONNECT, nodeId);
        record.getMetadata().putAll(metadata);
        context.changes.add(record);
        
        log.debug("记录连接变更: {}", nodeId);
    }
    
    /**
     * 提交工作单元
     * @return 是否提交成功
     */
    public boolean commit() {
        UnitOfWorkContext context = getCurrentContext();
        
        if (context.committed || context.rolledBack) {
            throw new IllegalStateException("工作单元已经提交或回滚");
        }
        
        try {
            // 提交前验证
            WorkflowValidator.ValidationResult validationResult = validator.validate(context.processNode);
            if (!validationResult.isValid()) {
                log.error("工作单元提交前验证失败: {}", validationResult.getErrors());
                rollback();
                return false;
            }
            
            // 应用所有变更（实际上变更已经在操作过程中应用了）
            applyChanges(context);
            
            context.committed = true;
            
            log.debug("工作单元提交成功，变更数量: {}", context.changes.size());
            return true;
            
        } catch (Exception e) {
            log.error("工作单元提交失败", e);
            rollback();
            return false;
        } finally {
            contextHolder.remove();
        }
    }
    
    /**
     * 回滚工作单元
     */
    public void rollback() {
        UnitOfWorkContext context = getCurrentContext();
        
        if (context.committed) {
            throw new IllegalStateException("工作单元已经提交，无法回滚");
        }
        
        if (context.rolledBack) {
            return; // 已经回滚过了
        }
        
        try {
            // 回滚所有变更
            rollbackChanges(context);
            
            context.rolledBack = true;
            
            log.debug("工作单元回滚成功，变更数量: {}", context.changes.size());
            
        } catch (Exception e) {
            log.error("工作单元回滚失败", e);
        } finally {
            contextHolder.remove();
        }
    }
    
    /**
     * 获取当前上下文
     */
    private UnitOfWorkContext getCurrentContext() {
        UnitOfWorkContext context = contextHolder.get();
        if (context == null) {
            throw new IllegalStateException("工作单元未开始");
        }
        return context;
    }
    
    /**
     * 应用变更
     */
    private void applyChanges(UnitOfWorkContext context) {
        // 在我们的实现中，变更在操作过程中已经应用到ProcessNode
        // 这里可以添加额外的持久化逻辑，比如保存到数据库
        
        for (ChangeRecord change : context.changes) {
            switch (change.getOperationType()) {
                case CREATE -> {
                    // 节点已经添加到flowNodeMap
                    log.debug("应用创建: {}", change.getNodeId());
                }
                case UPDATE -> {
                    // 节点已经更新
                    log.debug("应用更新: {}", change.getNodeId());
                }
                case DELETE -> {
                    // 节点已经从flowNodeMap删除
                    log.debug("应用删除: {}", change.getNodeId());
                }
                case CONNECT -> {
                    // 连接已经更新
                    log.debug("应用连接变更: {}", change.getNodeId());
                }
            }
        }
    }
    
    /**
     * 回滚变更
     */
    private void rollbackChanges(UnitOfWorkContext context) {
        // 按相反顺序回滚变更
        List<ChangeRecord> reversedChanges = new ArrayList<>(context.changes);
        Collections.reverse(reversedChanges);
        
        for (ChangeRecord change : reversedChanges) {
            switch (change.getOperationType()) {
                case CREATE -> {
                    // 删除创建的节点
                    context.processNode.getFlowNodeMap().remove(change.getNodeId());
                    log.debug("回滚创建: {}", change.getNodeId());
                }
                case UPDATE -> {
                    // 恢复原始节点
                    BaseNode originalNode = context.originalNodes.get(change.getNodeId());
                    if (originalNode != null) {
                        context.processNode.getFlowNodeMap().put(change.getNodeId(), originalNode);
                    }
                    log.debug("回滚更新: {}", change.getNodeId());
                }
                case DELETE -> {
                    // 恢复删除的节点
                    if (change.getOldNode() != null) {
                        context.processNode.getFlowNodeMap().put(change.getNodeId(), change.getOldNode());
                    }
                    log.debug("回滚删除: {}", change.getNodeId());
                }
                case CONNECT -> {
                    // 回滚连接变更（需要根据metadata恢复）
                    log.debug("回滚连接变更: {}", change.getNodeId());
                }
            }
        }
    }
    
    /**
     * 克隆节点（简单实现）
     */
    private BaseNode cloneNode(BaseNode node) {
        // 这里应该实现深拷贝
        // 为了简化，返回原节点（实际应用中需要完整的克隆实现）
        return node;
    }
    
    /**
     * 检查是否在工作单元中
     * @return 是否在工作单元中
     */
    public boolean isInTransaction() {
        return contextHolder.get() != null;
    }
    
    /**
     * 获取变更数量
     * @return 变更数量
     */
    public int getChangeCount() {
        UnitOfWorkContext context = contextHolder.get();
        return context != null ? context.changes.size() : 0;
    }
    
    /**
     * 获取变更记录（只读）
     * @return 变更记录列表
     */
    public List<ChangeRecord> getChanges() {
        UnitOfWorkContext context = contextHolder.get();
        return context != null ? new ArrayList<>(context.changes) : Collections.emptyList();
    }
}
